# GateSentinel 登录指南

## 🔧 问题修复

我已经修复了之前的认证问题：

### 修复内容
1. **路由配置错误**: 登录API路径被错误地放在了需要认证的路由组中
2. **API路径调整**: 将登录API移到了无需认证的路由组
3. **根路径重定向**: 添加了从根路径到登录页面的自动重定向

### 修复后的路由结构
```
/                           -> 重定向到 /web/login
/web/login                  -> 登录页面 (无需认证)
/web/admin/api/login        -> 登录API (无需认证)
/web/admin/beacons          -> Beacon管理页面 (需要认证)
/web/admin/api/beacons      -> Beacon API (需要认证)
```

## 🚀 如何测试

### 方法1: 使用测试页面
1. 访问: `http://127.0.0.1:8080/static/html/test-login.html`
2. 使用默认账户测试登录功能
3. 查看详细的测试结果

### 方法2: 直接使用系统
1. 访问: `http://127.0.0.1:8080`
2. 自动跳转到登录页面
3. 使用以下账户登录:
   - **用户名**: `admin`
   - **密码**: `admin123`

### 方法3: API测试
使用curl或Postman测试API：

```bash
# 登录获取Token
curl -X POST http://127.0.0.1:8080/web/admin/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 使用Token访问Beacon列表
curl -X GET http://127.0.0.1:8080/web/admin/api/beacons \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 📋 默认配置

### 管理员账户
- **用户名**: `admin`
- **密码**: `admin123`
- **位置**: `server/config/config.go`

### 服务器配置
- **端口**: `8080`
- **数据库**: `./shellgate.db`
- **客户端Token**: `Demo`

## 🔍 故障排除

### 如果仍然遇到401错误
1. **检查服务器是否重启**: 确保使用了修复后的路由配置
2. **检查API路径**: 确保使用 `/web/admin/api/login` 而不是其他路径
3. **检查请求格式**: 确保发送JSON格式的用户名和密码

### 如果登录成功但无法访问其他页面
1. **检查Token**: 确保Token正确保存在localStorage中
2. **检查请求头**: 确保API请求包含正确的Authorization头
3. **检查Token格式**: 应该是 `Bearer YOUR_TOKEN`

### 常见错误信息
- `{"error":"Unauthorized"}` - 未提供Token或Token无效
- `{"error":"Invalid token format"}` - Token格式错误
- `{"error":"Invalid credentials"}` - 用户名或密码错误

## 🎯 测试步骤

### 完整测试流程
1. **启动服务器**
   ```bash
   cd server
   go run main.go
   ```

2. **访问测试页面**
   - 打开浏览器访问: `http://127.0.0.1:8080/static/html/test-login.html`

3. **测试登录**
   - 点击"测试登录"按钮
   - 查看是否返回Token

4. **测试API访问**
   - 登录成功后点击"测试获取Beacon列表"
   - 查看是否能正常获取数据

5. **测试前端界面**
   - 访问: `http://127.0.0.1:8080`
   - 使用admin/admin123登录
   - 查看是否能正常访问管理界面

## 📊 预期结果

### 登录成功
```json
{
  "status": "success",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Beacon列表
```json
{
  "status": "success",
  "data": [
    {
      "uuid": "beacon-uuid-here",
      "hostname": "target-host",
      "ip": "*************",
      "username": "user",
      "process_name": "notepad.exe",
      "arch": "x64",
      "first_time": 1625097600000,
      "last_seen": 1625097600000,
      "job": null,
      "job_result": null
    }
  ]
}
```

## 🔧 开发者信息

### 前端模块
- **通用功能**: `/static/js/common.js`
- **组件库**: `/static/js/components.js`
- **安全模块**: `/static/js/security.js`
- **测试套件**: `/static/js/tests.js`

### 主要特性
- ✅ 现代化CSS设计系统
- ✅ 暗色主题支持
- ✅ 响应式设计
- ✅ XSS防护
- ✅ 模块化架构
- ✅ 自动化测试

现在您可以正常使用系统了！如果还有任何问题，请查看浏览器控制台的错误信息。
