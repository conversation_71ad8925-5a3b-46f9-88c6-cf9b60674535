/**
 * GateSentinel 前端测试套件
 * 简单的单元测试和集成测试
 */

// ===== 简单测试框架 =====
class SimpleTest {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            total: 0
        };
    }

    describe(description, testFn) {
        console.group(`🧪 ${description}`);
        testFn();
        console.groupEnd();
    }

    it(description, testFn) {
        this.results.total++;
        try {
            testFn();
            this.results.passed++;
            console.log(`✅ ${description}`);
        } catch (error) {
            this.results.failed++;
            console.error(`❌ ${description}`, error);
        }
    }

    expect(actual) {
        return {
            toBe: (expected) => {
                if (actual !== expected) {
                    throw new Error(`Expected ${expected}, but got ${actual}`);
                }
            },
            toEqual: (expected) => {
                if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                    throw new Error(`Expected ${JSON.stringify(expected)}, but got ${JSON.stringify(actual)}`);
                }
            },
            toBeTruthy: () => {
                if (!actual) {
                    throw new Error(`Expected truthy value, but got ${actual}`);
                }
            },
            toBeFalsy: () => {
                if (actual) {
                    throw new Error(`Expected falsy value, but got ${actual}`);
                }
            },
            toContain: (expected) => {
                if (!actual.includes(expected)) {
                    throw new Error(`Expected ${actual} to contain ${expected}`);
                }
            },
            toThrow: () => {
                let threw = false;
                try {
                    actual();
                } catch (e) {
                    threw = true;
                }
                if (!threw) {
                    throw new Error('Expected function to throw an error');
                }
            }
        };
    }

    run() {
        console.log('\n📊 测试结果:');
        console.log(`总计: ${this.results.total}`);
        console.log(`通过: ${this.results.passed}`);
        console.log(`失败: ${this.results.failed}`);
        console.log(`成功率: ${((this.results.passed / this.results.total) * 100).toFixed(2)}%`);
        
        return this.results.failed === 0;
    }
}

// ===== 测试用例 =====
function runTests() {
    const test = new SimpleTest();

    // 工具函数测试
    test.describe('Utils 工具函数测试', () => {
        test.it('应该正确格式化日期', () => {
            const now = Date.now();
            const formatted = GateSentinel.Utils.formatDate(now);
            test.expect(typeof formatted).toBe('string');
            test.expect(formatted).toContain('前');
        });

        test.it('应该正确验证UUID', () => {
            const validUUID = '123e4567-e89b-12d3-a456-************';
            const invalidUUID = 'invalid-uuid';
            
            test.expect(GateSentinel.Utils.isValidUUID(validUUID)).toBeTruthy();
            test.expect(GateSentinel.Utils.isValidUUID(invalidUUID)).toBeFalsy();
        });

        test.it('应该正确格式化文件大小', () => {
            test.expect(GateSentinel.Utils.formatFileSize(1024)).toBe('1 KB');
            test.expect(GateSentinel.Utils.formatFileSize(1048576)).toBe('1 MB');
            test.expect(GateSentinel.Utils.formatFileSize(0)).toBe('0 Bytes');
        });
    });

    // XSS防护测试
    test.describe('XSS防护测试', () => {
        test.it('应该正确转义HTML字符', () => {
            const input = '<script>alert("xss")</script>';
            const escaped = GateSentinel.Security.XSSProtection.escapeHtml(input);
            test.expect(escaped).toContain('&lt;script&gt;');
            test.expect(escaped).toContain('&lt;/script&gt;');
        });

        test.it('应该清理危险的输入', () => {
            const input = '<script>alert("xss")</script><p>safe content</p>';
            const sanitized = GateSentinel.Security.XSSProtection.sanitizeInput(input);
            test.expect(sanitized).not.toContain('<script>');
            test.expect(sanitized).toContain('safe content');
        });

        test.it('应该验证输入格式', () => {
            test.expect(GateSentinel.Security.XSSProtection.validateInput('***********', 'ip')).toBeTruthy();
            test.expect(GateSentinel.Security.XSSProtection.validateInput('invalid-ip', 'ip')).toBeFalsy();
            test.expect(GateSentinel.Security.XSSProtection.validateInput('example.com', 'hostname')).toBeTruthy();
        });
    });

    // API客户端测试
    test.describe('API客户端测试', () => {
        test.it('应该正确设置token', () => {
            const testToken = 'test-token-123';
            GateSentinel.apiClient.setToken(testToken);
            test.expect(GateSentinel.apiClient.token).toBe(testToken);
            test.expect(localStorage.getItem('token')).toBe(testToken);
        });

        test.it('应该正确清除token', () => {
            GateSentinel.apiClient.setToken('test-token');
            GateSentinel.apiClient.clearToken();
            test.expect(GateSentinel.apiClient.token).toBe(null);
            test.expect(localStorage.getItem('token')).toBe(null);
        });
    });

    // 通知系统测试
    test.describe('通知系统测试', () => {
        test.it('应该能创建通知容器', () => {
            const container = GateSentinel.notification.createContainer();
            test.expect(container.className).toBe('notification-container');
        });

        test.it('应该能显示不同类型的通知', () => {
            const notification = GateSentinel.notification.show('测试消息', 'success', 1000);
            test.expect(notification.classList.contains('alert-success')).toBeTruthy();
            
            // 清理
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 100);
        });
    });

    // 主题管理测试
    test.describe('主题管理测试', () => {
        test.it('应该能切换主题', () => {
            const currentTheme = GateSentinel.themeManager.currentTheme;
            GateSentinel.themeManager.toggle();
            const newTheme = GateSentinel.themeManager.currentTheme;
            test.expect(currentTheme).not.toBe(newTheme);
            
            // 恢复原主题
            GateSentinel.themeManager.toggle();
        });

        test.it('应该能应用主题', () => {
            GateSentinel.themeManager.applyTheme('dark');
            test.expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
            test.expect(localStorage.getItem('theme')).toBe('dark');
            
            // 恢复
            GateSentinel.themeManager.applyTheme('light');
        });
    });

    // 模态框组件测试
    test.describe('模态框组件测试', () => {
        test.it('应该能创建模态框', () => {
            const modal = new GateSentinel.Components.ModalComponent({
                id: 'testModal',
                title: '测试模态框',
                body: '测试内容'
            });
            
            modal.create();
            const element = document.getElementById('testModal');
            test.expect(element).toBeTruthy();
            test.expect(element.querySelector('.modal-title').textContent).toBe('测试模态框');
            
            // 清理
            modal.destroy();
        });

        test.it('应该能创建确认对话框', () => {
            let confirmed = false;
            const modal = GateSentinel.Components.ModalComponent.confirm(
                '确认测试',
                '这是一个测试确认对话框',
                () => { confirmed = true; }
            );
            
            const element = document.getElementById('confirmModal');
            test.expect(element).toBeTruthy();
            
            // 清理
            modal.destroy();
        });
    });

    // 输入验证测试
    test.describe('输入验证测试', () => {
        test.it('应该验证必填字段', () => {
            const errors = GateSentinel.Security.InputValidator.validateField(
                'username', '', { required: true, label: '用户名' }
            );
            test.expect(errors.length).toBe(1);
            test.expect(errors[0]).toContain('用户名 是必填项');
        });

        test.it('应该验证字段长度', () => {
            const errors = GateSentinel.Security.InputValidator.validateField(
                'password', '123', { minLength: 6, label: '密码' }
            );
            test.expect(errors.length).toBe(1);
            test.expect(errors[0]).toContain('长度不能少于 6 个字符');
        });

        test.it('应该验证字段格式', () => {
            const errors = GateSentinel.Security.InputValidator.validateField(
                'email', 'invalid-email', { 
                    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, 
                    label: '邮箱',
                    message: '邮箱格式不正确'
                }
            );
            test.expect(errors.length).toBe(1);
            test.expect(errors[0]).toContain('邮箱格式不正确');
        });
    });

    // 性能优化测试
    test.describe('性能优化测试', () => {
        test.it('应该能清理过期缓存', () => {
            // 添加一个过期的缓存项
            const expiredItem = {
                data: 'test',
                timestamp: Date.now() - (25 * 60 * 60 * 1000) // 25小时前
            };
            localStorage.setItem('cache_test_expired', JSON.stringify(expiredItem));
            
            // 清理过期缓存
            GateSentinel.Security.PerformanceOptimizer.cleanExpiredCache(24 * 60 * 60 * 1000);
            
            // 验证过期项已被删除
            test.expect(localStorage.getItem('cache_test_expired')).toBe(null);
        });
    });

    return test.run();
}

// ===== 性能测试 =====
function runPerformanceTests() {
    console.group('⚡ 性能测试');
    
    // 测试函数执行时间
    function measureTime(fn, name) {
        const start = performance.now();
        fn();
        const end = performance.now();
        console.log(`${name}: ${(end - start).toFixed(2)}ms`);
    }

    // 测试XSS防护性能
    measureTime(() => {
        for (let i = 0; i < 1000; i++) {
            GateSentinel.Security.XSSProtection.escapeHtml('<script>alert("test")</script>');
        }
    }, 'XSS防护 (1000次)');

    // 测试日期格式化性能
    measureTime(() => {
        for (let i = 0; i < 1000; i++) {
            GateSentinel.Utils.formatDate(Date.now());
        }
    }, '日期格式化 (1000次)');

    // 测试UUID验证性能
    measureTime(() => {
        for (let i = 0; i < 1000; i++) {
            GateSentinel.Utils.isValidUUID('123e4567-e89b-12d3-a456-************');
        }
    }, 'UUID验证 (1000次)');

    console.groupEnd();
}

// ===== 导出测试函数 =====
window.GateSentinel = window.GateSentinel || {};
window.GateSentinel.Tests = {
    runTests,
    runPerformanceTests,
    SimpleTest
};

// ===== 自动运行测试（仅在开发环境） =====
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
        // 延迟运行测试，确保所有模块都已加载
        setTimeout(() => {
            console.log('🚀 开始运行前端测试...\n');
            const success = runTests();
            runPerformanceTests();
            
            if (success) {
                console.log('\n🎉 所有测试通过！');
            } else {
                console.log('\n⚠️ 部分测试失败，请检查代码。');
            }
        }, 1000);
    });
}
