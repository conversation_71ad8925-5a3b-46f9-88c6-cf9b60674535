<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beacon POST测试 - GateSentinel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log-entry {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-warning { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        textarea {
            width: 100%;
            height: 100px;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <h1>🧪 Beacon POST请求测试</h1>
    
    <div class="test-card">
        <h3>📋 测试参数</h3>
        <div class="row">
            <div style="display: flex; gap: 20px; margin-bottom: 15px;">
                <div style="flex: 1;">
                    <label>Beacon UUID:</label>
                    <input type="text" id="beaconUUID" value="b9869be1-9054-4e5b-8022-f811e4f56f21" style="width: 100%; padding: 5px;">
                </div>
                <div style="flex: 1;">
                    <label>Token:</label>
                    <input type="text" id="clientToken" value="Demo" style="width: 100%; padding: 5px;">
                </div>
            </div>
            <div>
                <label>任务结果 (Base64编码):</label>
                <textarea id="taskResult" placeholder="输入任务结果，将自动进行Base64编码">Process List:
notepad.exe - PID: 1234
chrome.exe - PID: 5678
explorer.exe - PID: 9012</textarea>
            </div>
        </div>
    </div>

    <div class="test-card">
        <h3>🚀 测试操作</h3>
        <button onclick="testGETRequest()">测试GET请求 (获取任务)</button>
        <button onclick="testPOSTRequest()">测试POST请求 (提交结果)</button>
        <button onclick="testFullFlow()">测试完整流程</button>
        <button onclick="clearLogs()">清空日志</button>
    </div>

    <div class="test-card">
        <h3>📝 测试日志</h3>
        <div id="logContainer" style="max-height: 400px; overflow-y: auto;">
            <!-- 日志将在这里显示 -->
        </div>
    </div>

    <script>
        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }

        function getBeaconUUID() {
            return document.getElementById('beaconUUID').value.trim();
        }

        function getClientToken() {
            return document.getElementById('clientToken').value.trim();
        }

        function getTaskResult() {
            const result = document.getElementById('taskResult').value.trim();
            return btoa(result); // Base64编码
        }

        async function testGETRequest() {
            const uuid = getBeaconUUID();
            const token = getClientToken();
            
            addLog(`🔍 测试GET请求 - UUID: ${uuid}`, 'info');
            
            try {
                const response = await fetch(`/api.jsp?clientId=${uuid}`, {
                    method: 'GET',
                    headers: {
                        'X-Client-Token': token
                    }
                });

                const responseText = await response.text();
                
                if (response.ok) {
                    addLog(`✅ GET请求成功 - 状态: ${response.status}`, 'success');
                    addLog(`📄 响应内容: ${responseText}`, 'info');
                } else {
                    addLog(`❌ GET请求失败 - 状态: ${response.status}`, 'error');
                    addLog(`📄 错误内容: ${responseText}`, 'error');
                }
            } catch (error) {
                addLog(`❌ GET请求异常: ${error.message}`, 'error');
            }
        }

        async function testPOSTRequest() {
            const uuid = getBeaconUUID();
            const token = getClientToken();
            const result = getTaskResult();
            
            addLog(`📤 测试POST请求 - UUID: ${uuid}`, 'info');
            addLog(`📋 任务结果长度: ${result.length} 字符 (Base64编码)`, 'info');
            
            try {
                const response = await fetch(`/api.jsp?clientId=${uuid}`, {
                    method: 'POST',
                    headers: {
                        'X-Client-Token': token,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: result
                });

                const responseText = await response.text();
                
                if (response.ok) {
                    addLog(`✅ POST请求成功 - 状态: ${response.status}`, 'success');
                    addLog(`📄 响应内容: ${responseText}`, 'info');
                } else {
                    addLog(`❌ POST请求失败 - 状态: ${response.status}`, 'error');
                    addLog(`📄 错误内容: ${responseText}`, 'error');
                }
            } catch (error) {
                addLog(`❌ POST请求异常: ${error.message}`, 'error');
            }
        }

        async function testFullFlow() {
            addLog(`🔄 开始完整流程测试`, 'info');
            
            // 1. 先测试GET请求
            await testGETRequest();
            
            // 等待1秒
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 2. 再测试POST请求
            await testPOSTRequest();
            
            // 等待1秒
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 3. 再次GET请求检查状态
            addLog(`🔍 再次检查状态...`, 'info');
            await testGETRequest();
            
            addLog(`✨ 完整流程测试完成`, 'success');
        }

        async function checkBeaconStatus() {
            const uuid = getBeaconUUID();
            
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    addLog('❌ 未找到管理员Token，请先登录', 'error');
                    return;
                }

                const response = await fetch(`/web/admin/api/beacons/${uuid}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const beacon = await response.json();
                    addLog(`📊 Beacon状态: 任务=${beacon.job || '无'}, 结果=${beacon.job_result ? '有' : '无'}`, 'info');
                    
                    if (beacon.job_result) {
                        addLog(`📋 任务结果预览: ${beacon.job_result.substring(0, 100)}...`, 'success');
                    }
                } else {
                    addLog(`❌ 获取Beacon状态失败: ${response.status}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 检查状态异常: ${error.message}`, 'error');
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 Beacon POST测试页面已加载', 'info');
            addLog('💡 提示: 先下发一个任务，然后使用此页面模拟客户端提交结果', 'warning');
        });
    </script>
</body>
</html>
