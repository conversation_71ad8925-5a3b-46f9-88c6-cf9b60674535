/**
 * Beacon管理页面模块
 * 处理Beacon列表显示、任务下发等功能
 */

class BeaconManager {
    constructor() {
        this.beaconList = document.getElementById('beaconList');
        this.taskModal = null;
        this.refreshInterval = null;
        this.beacons = [];
        this.filteredBeacons = [];
        this.currentFilter = 'all';

        this.init();
    }

    init() {
        // 检查认证
        if (!GateSentinel.apiClient.token) {
            window.location.href = '/login';
            return;
        }

        // 初始化任务模态框
        this.initTaskModal();

        // 加载Beacon列表
        this.loadBeacons();

        // 设置定时刷新
        this.startAutoRefresh();

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            this.stopAutoRefresh();
        });
    }

    initTaskModal() {
        const taskModalEl = document.getElementById('taskModal');
        if (taskModalEl) {
            this.taskModal = new bootstrap.Modal(taskModalEl);

            // 绑定任务类型选择事件
            const taskTypeSelect = document.getElementById('taskType');
            if (taskTypeSelect) {
                taskTypeSelect.addEventListener('change', () => this.handleTaskTypeChange());
            }

            // 绑定发送任务按钮
            const sendTaskBtn = document.getElementById('sendTask');
            if (sendTaskBtn) {
                sendTaskBtn.addEventListener('click', () => this.handleSendTask());
            }
        }
    }

    async loadBeacons() {
        try {
            // 显示骨架屏
            this.showSkeleton();

            const data = await GateSentinel.apiClient.get('/web/admin/api/beacons');

            this.beacons = data.data || [];
            this.applyFilter();
            this.updateStatistics();
            this.renderBeacons();

        } catch (error) {
            console.error('加载Beacon列表失败:', error);
            GateSentinel.notification.error('加载Beacon列表失败: ' + error.message);
            this.showError('加载失败，请刷新页面重试');
        }
    }

    renderBeacons() {
        if (!this.beaconList) return;

        const beaconsToShow = this.filteredBeacons.length > 0 ? this.filteredBeacons : this.beacons;

        if (beaconsToShow.length === 0) {
            const message = this.currentFilter === 'all' ?
                '暂无Beacon连接' :
                `暂无${this.getFilterName(this.currentFilter)}的Beacon`;

            this.beaconList.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted py-5">
                        <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                        <div class="mt-3">${message}</div>
                        ${this.currentFilter !== 'all' ?
                            '<button class="btn btn-outline-primary btn-sm mt-2" onclick="beaconManager.filterByStatus(\'all\')">显示全部</button>' :
                            ''}
                    </td>
                </tr>
            `;
            return;
        }

        this.beaconList.innerHTML = '';

        beaconsToShow.forEach(beacon => {
            const row = this.createBeaconRow(beacon);
            this.beaconList.appendChild(row);
        });
    }

    applyFilter() {
        if (this.currentFilter === 'all') {
            this.filteredBeacons = [...this.beacons];
        } else {
            this.filteredBeacons = this.beacons.filter(beacon => {
                const status = this.getStatusInfo(beacon.last_seen).class;
                return status === this.currentFilter;
            });
        }
    }

    filterByStatus(status) {
        this.currentFilter = status;
        this.applyFilter();
        this.renderBeacons();
    }

    getFilterName(filter) {
        const names = {
            'online': '在线',
            'warning': '离线',
            'offline': '断线'
        };
        return names[filter] || filter;
    }

    updateStatistics() {
        const stats = {
            online: 0,
            offline: 0,
            disconnected: 0,
            total: this.beacons.length
        };

        this.beacons.forEach(beacon => {
            const status = this.getStatusInfo(beacon.last_seen).class;
            if (status === 'online') {
                stats.online++;
            } else if (status === 'warning') {
                stats.offline++;
            } else {
                stats.disconnected++;
            }
        });

        // 更新统计卡片
        this.updateStatCard('onlineCount', stats.online);
        this.updateStatCard('offlineCount', stats.offline);
        this.updateStatCard('disconnectedCount', stats.disconnected);
        this.updateStatCard('totalCount', stats.total);
    }

    updateStatCard(elementId, count) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = count;
        }
    }

    createBeaconRow(beacon) {
        const row = document.createElement('tr');
        row.className = 'fade-in';
        row.setAttribute('data-beacon-id', beacon.uuid);

        const statusInfo = this.getStatusInfo(beacon.last_seen);

        row.innerHTML = `
            <td>
                <div class="beacon-status ${statusInfo.class}">
                    <div class="status-dot ${statusInfo.class}"></div>
                    ${statusInfo.text}
                </div>
            </td>
            <td>
                <div class="d-flex flex-column">
                    <span class="font-mono font-semibold">${this.escapeHtml(beacon.hostname || '-')}</span>
                    <small class="text-muted">
                        <i class="bi bi-person me-1"></i>${this.escapeHtml(beacon.username || '-')}
                    </small>
                </div>
            </td>
            <td>
                <div class="d-flex flex-column">
                    <span class="font-mono">${this.escapeHtml(beacon.ip || '-')}</span>
                    <small class="text-muted">
                        <i class="bi bi-geo me-1"></i>${this.getLocationInfo(beacon.ip)}
                    </small>
                </div>
            </td>
            <td>
                <div class="d-flex flex-column">
                    <span class="font-mono">${this.escapeHtml(beacon.process_name || '-')}</span>
                    <small class="text-muted">
                        <i class="bi bi-cpu me-1"></i>${this.escapeHtml(beacon.arch || '-')}
                        ${beacon.process_id ? `| PID: ${beacon.process_id}` : ''}
                    </small>
                </div>
            </td>
            <td>
                <div class="d-flex flex-column">
                    <small class="text-muted">
                        <i class="bi bi-clock-history me-1"></i>首次: ${GateSentinel.Utils.formatDate(beacon.first_time)}
                    </small>
                    <small class="text-muted">
                        <i class="bi bi-clock me-1"></i>最近: ${GateSentinel.Utils.formatDate(beacon.last_seen)}
                    </small>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge ${beacon.job ? 'bg-warning' : 'bg-secondary'}">
                        ${beacon.job ? '有任务' : '空闲'}
                    </span>
                    ${beacon.job ? `<i class="bi bi-exclamation-circle text-warning" title="${this.escapeHtml(beacon.job)}"></i>` : ''}
                </div>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-primary" onclick="beaconManager.showTaskModal('${beacon.uuid}')" title="下发任务">
                        <i class="bi bi-send"></i>
                    </button>
                    <button class="btn btn-info" onclick="beaconManager.viewDetails('${beacon.uuid}')" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-danger" onclick="beaconManager.deleteBeacon('${beacon.uuid}')" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;

        return row;
    }

    getLocationInfo(ip) {
        if (!ip || ip === '-') return '未知';

        // 简单的IP地址分类
        if (ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
            return '内网';
        } else if (ip.startsWith('127.')) {
            return '本地';
        } else {
            return '外网';
        }
    }

    getStatusInfo(lastSeen) {
        if (!lastSeen) {
            return { class: 'offline', text: '未知' };
        }

        try {
            const lastSeenMs = typeof lastSeen === 'string' ? Number(lastSeen) : lastSeen;
            const now = Date.now();
            const diffSeconds = Math.floor((now - lastSeenMs) / 1000);

            if (diffSeconds < 300) { // 5分钟内
                return { class: 'online', text: '在线' };
            } else if (diffSeconds < 900) { // 15分钟内
                return { class: 'warning', text: '离线' };
            } else {
                return { class: 'offline', text: '断线' };
            }
        } catch (error) {
            console.error('状态检查失败:', error);
            return { class: 'offline', text: '错误' };
        }
    }

    showSkeleton() {
        if (!this.beaconList) return;

        this.beaconList.innerHTML = Array(5).fill(0).map(() => `
            <tr>
                <td>
                    <div class="d-flex align-items-center gap-2">
                        <div class="skeleton" style="width: 12px; height: 12px; border-radius: 50%;"></div>
                        <div class="skeleton skeleton-text w-1-2"></div>
                    </div>
                </td>
                <td>
                    <div class="skeleton skeleton-text w-3-4 mb-1"></div>
                    <div class="skeleton skeleton-text w-1-2"></div>
                </td>
                <td>
                    <div class="skeleton skeleton-text w-full mb-1"></div>
                    <div class="skeleton skeleton-text w-1-2"></div>
                </td>
                <td>
                    <div class="skeleton skeleton-text w-3-4 mb-1"></div>
                    <div class="skeleton skeleton-text w-1-2"></div>
                </td>
                <td>
                    <div class="skeleton skeleton-text w-full mb-1"></div>
                    <div class="skeleton skeleton-text w-3-4"></div>
                </td>
                <td>
                    <div class="skeleton skeleton-text w-1-2"></div>
                </td>
                <td>
                    <div class="d-flex gap-1">
                        <div class="skeleton" style="width: 32px; height: 24px; border-radius: 4px;"></div>
                        <div class="skeleton" style="width: 32px; height: 24px; border-radius: 4px;"></div>
                        <div class="skeleton" style="width: 32px; height: 24px; border-radius: 4px;"></div>
                    </div>
                </td>
            </tr>
        `).join('');

        // 更新统计卡片为加载状态
        ['onlineCount', 'offlineCount', 'disconnectedCount', 'totalCount'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = '<div class="loading-spinner"></div>';
            }
        });
    }

    showError(message) {
        if (!this.beaconList) return;

        this.beaconList.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-5">
                    <div class="text-danger">
                        <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                        <div class="mt-3 mb-3">${message}</div>
                        <button class="btn btn-outline-primary" onclick="beaconManager.loadBeacons()">
                            <i class="bi bi-arrow-clockwise me-2"></i>重新加载
                        </button>
                    </div>
                </td>
            </tr>
        `;

        // 重置统计卡片
        ['onlineCount', 'offlineCount', 'disconnectedCount', 'totalCount'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = '-';
            }
        });
    }

    escapeHtml(text) {
        // 安全的HTML转义函数，兼容安全模块未加载的情况
        if (window.GateSentinel && window.GateSentinel.Security && window.GateSentinel.Security.XSSProtection) {
            return window.GateSentinel.Security.XSSProtection.escapeHtml(text);
        }

        // 备用的HTML转义实现
        if (typeof text !== 'string') return text;

        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;',
            '/': '&#x2F;'
        };

        return text.replace(/[&<>"'/]/g, (s) => map[s]);
    }

    showTaskModal(uuid) {
        document.getElementById('beaconUUID').value = uuid;
        if (this.taskModal) {
            this.taskModal.show();
        }
    }

    handleTaskTypeChange() {
        const taskType = document.getElementById('taskType').value;
        const sleepTimeDiv = document.getElementById('sleepTimeDiv');
        const shellcodeDiv = document.getElementById('shellcodeDiv');

        sleepTimeDiv.style.display = taskType === '0x1A' ? 'block' : 'none';
        shellcodeDiv.style.display = taskType === '0x1C' ? 'block' : 'none';
    }

    async handleSendTask() {
        const uuid = document.getElementById('beaconUUID').value;
        const taskType = document.getElementById('taskType').value;

        if (!uuid) {
            GateSentinel.notification.warning('请选择要操作的Beacon');
            return;
        }

        let taskData = { type: taskType };

        // 根据任务类型处理不同的数据
        if (taskType === '0x1A') {
            const sleepTime = document.getElementById('sleepTime').value;
            if (!sleepTime || sleepTime <= 0) {
                GateSentinel.notification.warning('请输入有效的Sleep时间');
                return;
            }
            taskData.sleep_time = parseInt(sleepTime);
        } else if (taskType === '0x1C') {
            const file = document.getElementById('shellcodeFile').files[0];
            if (!file) {
                GateSentinel.notification.warning('请选择Shellcode文件');
                return;
            }

            try {
                const fileContent = await this.readFileAsDataURL(file);
                taskData.shellcode = fileContent;
            } catch (error) {
                GateSentinel.notification.error('文件读取失败: ' + error.message);
                return;
            }
        }

        await this.sendTaskToServer(uuid, taskData);
    }

    readFileAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('文件读取失败'));
            reader.readAsDataURL(file);
        });
    }

    async sendTaskToServer(uuid, taskData) {
        try {
            GateSentinel.loading.show('发送任务中...');

            await GateSentinel.apiClient.post(`/web/admin/api/beacons/${uuid}/job`, taskData);

            GateSentinel.notification.success('任务发送成功');

            // 隐藏模态框
            if (this.taskModal) {
                this.taskModal.hide();
            }

            // 重新加载列表
            this.loadBeacons();

        } catch (error) {
            console.error('发送任务失败:', error);
            GateSentinel.notification.error('发送任务失败: ' + error.message);
        } finally {
            GateSentinel.loading.hide();
        }
    }

    viewDetails(uuid) {
        window.location.href = `/web/admin/details/${uuid}`;
    }

    async deleteBeacon(uuid) {
        if (!confirm('确定要删除这个Beacon吗？此操作不可恢复。')) {
            return;
        }

        try {
            GateSentinel.loading.show('删除中...');

            await GateSentinel.apiClient.post(`/web/admin/api/beacons/${uuid}/delete`);

            GateSentinel.notification.success('Beacon删除成功');
            this.loadBeacons();

        } catch (error) {
            console.error('删除Beacon失败:', error);
            GateSentinel.notification.error('删除失败: ' + error.message);
        } finally {
            GateSentinel.loading.hide();
        }
    }

    startAutoRefresh() {
        // 每30秒刷新一次
        this.refreshInterval = setInterval(() => {
            this.loadBeacons();
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
}

// 全局实例变量
let beaconManager = null;

// 初始化函数
function initBeaconManager() {
    if (!beaconManager) {
        beaconManager = new BeaconManager();
    }
    return beaconManager;
}

// 确保在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保所有模块都已加载
    setTimeout(() => {
        initBeaconManager();
    }, 100);
});

// 兼容旧的函数调用方式
function loadBeacons() {
    const manager = beaconManager || initBeaconManager();
    manager.loadBeacons();
}

function showTaskModal(uuid) {
    const manager = beaconManager || initBeaconManager();
    manager.showTaskModal(uuid);
}

function viewDetails(uuid) {
    const manager = beaconManager || initBeaconManager();
    manager.viewDetails(uuid);
}