#ifndef _TASKS_H_
#define _TASKS_H_

#include <windows.h>

// 全局变量（在beacon.c中定义）
extern DWORD g_sleep_time;

// 任务类型定义
#define TASK_NULL        0x00    // 保持Sleep
#define TASK_SET_SLEEP   0x1A    // 设置Sleep时间
#define TASK_PROC_LIST   0x1B    // 获取进程列表
#define TASK_SHELLCODE   0x1C    // 执行Shellcode

// 任务结果状态
#define TASK_STATUS_SUCCESS  0   // 任务执行成功
#define TASK_STATUS_FAILED   1   // 任务执行失败

// 函数声明
BOOL Task_SetSleep(DWORD sleepTime);
BOOL GetProcessList(char* output, DWORD outputSize);
BOOL ExecuteShellcode(const unsigned char* shellcode, SIZE_T shellcodeSize);

#endif // _TASKS_H_ 