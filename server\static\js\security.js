/**
 * GateSentinel 安全模块
 * 提供XSS防护、输入验证、安全策略等功能
 */

// ===== XSS防护 =====
class XSSProtection {
    static escapeHtml(text) {
        if (typeof text !== 'string') return text;
        
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;',
            '/': '&#x2F;'
        };
        
        return text.replace(/[&<>"'/]/g, (s) => map[s]);
    }

    static sanitizeInput(input, options = {}) {
        if (typeof input !== 'string') return input;
        
        let sanitized = input;
        
        // 移除危险的HTML标签
        if (options.stripTags !== false) {
            sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            sanitized = sanitized.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '');
            sanitized = sanitized.replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '');
            sanitized = sanitized.replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '');
            sanitized = sanitized.replace(/<link\b[^<]*(?:(?!<\/link>)<[^<]*)*<\/link>/gi, '');
            sanitized = sanitized.replace(/<meta\b[^<]*(?:(?!<\/meta>)<[^<]*)*<\/meta>/gi, '');
        }
        
        // 移除危险的事件处理器
        sanitized = sanitized.replace(/on\w+\s*=\s*["'][^"']*["']/gi, '');
        sanitized = sanitized.replace(/javascript:/gi, '');
        
        // 移除危险的URL协议
        sanitized = sanitized.replace(/data:/gi, '');
        sanitized = sanitized.replace(/vbscript:/gi, '');
        
        return sanitized;
    }

    static validateInput(input, type) {
        const validators = {
            uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
            ip: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            hostname: /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/,
            username: /^[a-zA-Z0-9_\-\.]{1,50}$/,
            url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
        };
        
        if (!validators[type]) {
            console.warn(`Unknown validation type: ${type}`);
            return true;
        }
        
        return validators[type].test(input);
    }
}

// ===== 输入验证 =====
class InputValidator {
    static validateForm(formElement, rules) {
        const errors = [];
        const formData = new FormData(formElement);
        
        for (const [field, rule] of Object.entries(rules)) {
            const value = formData.get(field);
            const fieldErrors = this.validateField(field, value, rule);
            errors.push(...fieldErrors);
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    static validateField(fieldName, value, rule) {
        const errors = [];
        
        // 必填验证
        if (rule.required && (!value || value.trim() === '')) {
            errors.push(`${rule.label || fieldName} 是必填项`);
            return errors;
        }
        
        // 如果值为空且非必填，跳过其他验证
        if (!value || value.trim() === '') {
            return errors;
        }
        
        // 长度验证
        if (rule.minLength && value.length < rule.minLength) {
            errors.push(`${rule.label || fieldName} 长度不能少于 ${rule.minLength} 个字符`);
        }
        
        if (rule.maxLength && value.length > rule.maxLength) {
            errors.push(`${rule.label || fieldName} 长度不能超过 ${rule.maxLength} 个字符`);
        }
        
        // 模式验证
        if (rule.pattern && !rule.pattern.test(value)) {
            errors.push(rule.message || `${rule.label || fieldName} 格式不正确`);
        }
        
        // 类型验证
        if (rule.type && !XSSProtection.validateInput(value, rule.type)) {
            errors.push(`${rule.label || fieldName} 格式不正确`);
        }
        
        // 自定义验证
        if (rule.validator && typeof rule.validator === 'function') {
            const customResult = rule.validator(value);
            if (customResult !== true) {
                errors.push(customResult || `${rule.label || fieldName} 验证失败`);
            }
        }
        
        return errors;
    }

    static showFieldError(fieldElement, message) {
        this.clearFieldError(fieldElement);
        
        fieldElement.classList.add('is-invalid');
        
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        
        fieldElement.parentNode.appendChild(feedback);
    }

    static clearFieldError(fieldElement) {
        fieldElement.classList.remove('is-invalid');
        
        const feedback = fieldElement.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }

    static clearAllErrors(formElement) {
        const invalidFields = formElement.querySelectorAll('.is-invalid');
        invalidFields.forEach(field => this.clearFieldError(field));
    }
}

// ===== 安全策略 =====
class SecurityPolicy {
    static init() {
        this.setupCSP();
        this.setupSecurityHeaders();
        this.preventClickjacking();
        this.setupFormProtection();
    }

    static setupCSP() {
        // 如果页面没有CSP，添加基本的CSP策略
        if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
            const meta = document.createElement('meta');
            meta.httpEquiv = 'Content-Security-Policy';
            meta.content = [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net",
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net",
                "font-src 'self' https://cdn.jsdelivr.net",
                "img-src 'self' data:",
                "connect-src 'self'",
                "frame-ancestors 'none'"
            ].join('; ');
            
            document.head.appendChild(meta);
        }
    }

    static setupSecurityHeaders() {
        // 添加安全相关的meta标签
        const securityMetas = [
            { name: 'referrer', content: 'strict-origin-when-cross-origin' },
            { httpEquiv: 'X-Content-Type-Options', content: 'nosniff' },
            { httpEquiv: 'X-Frame-Options', content: 'DENY' },
            { httpEquiv: 'X-XSS-Protection', content: '1; mode=block' }
        ];

        securityMetas.forEach(meta => {
            if (!document.querySelector(`meta[${meta.name ? 'name' : 'http-equiv'}="${meta.name || meta.httpEquiv}"]`)) {
                const element = document.createElement('meta');
                if (meta.name) element.name = meta.name;
                if (meta.httpEquiv) element.httpEquiv = meta.httpEquiv;
                element.content = meta.content;
                document.head.appendChild(element);
            }
        });
    }

    static preventClickjacking() {
        // 检测是否在iframe中运行
        if (window.top !== window.self) {
            // 如果在iframe中且不是同源，则跳转到顶层
            try {
                if (window.top.location.hostname !== window.location.hostname) {
                    window.top.location = window.location;
                }
            } catch (e) {
                // 跨域访问被阻止，直接跳转
                window.top.location = window.location;
            }
        }
    }

    static setupFormProtection() {
        // 为所有表单添加CSRF保护
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.tagName === 'FORM') {
                this.addCSRFToken(form);
            }
        });
    }

    static addCSRFToken(form) {
        // 检查是否已有CSRF token
        if (form.querySelector('input[name="csrf_token"]')) {
            return;
        }

        // 生成简单的CSRF token（实际项目中应该从服务器获取）
        const token = this.generateCSRFToken();
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = token;
        
        form.appendChild(csrfInput);
    }

    static generateCSRFToken() {
        // 简单的token生成（实际项目中应该使用更安全的方法）
        const array = new Uint8Array(32);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }
}

// ===== 性能优化 =====
class PerformanceOptimizer {
    static init() {
        this.setupResourceHints();
        this.setupLazyLoading();
        this.setupCaching();
        this.monitorPerformance();
    }

    static setupResourceHints() {
        // 添加DNS预解析
        const domains = ['cdn.jsdelivr.net'];
        domains.forEach(domain => {
            if (!document.querySelector(`link[rel="dns-prefetch"][href*="${domain}"]`)) {
                const link = document.createElement('link');
                link.rel = 'dns-prefetch';
                link.href = `//${domain}`;
                document.head.appendChild(link);
            }
        });

        // 预连接到重要的外部资源
        const preconnectDomains = ['https://cdn.jsdelivr.net'];
        preconnectDomains.forEach(domain => {
            if (!document.querySelector(`link[rel="preconnect"][href="${domain}"]`)) {
                const link = document.createElement('link');
                link.rel = 'preconnect';
                link.href = domain;
                link.crossOrigin = 'anonymous';
                document.head.appendChild(link);
            }
        });
    }

    static setupLazyLoading() {
        // 为图片添加懒加载
        const images = document.querySelectorAll('img[data-src]');
        if (images.length > 0 && 'IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }
    }

    static setupCaching() {
        // 设置本地存储缓存策略
        const cacheConfig = {
            maxAge: 24 * 60 * 60 * 1000, // 24小时
            maxSize: 50 // 最多缓存50个项目
        };

        // 清理过期缓存
        this.cleanExpiredCache(cacheConfig.maxAge);
        
        // 限制缓存大小
        this.limitCacheSize(cacheConfig.maxSize);
    }

    static cleanExpiredCache(maxAge) {
        const now = Date.now();
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('cache_')) {
                try {
                    const item = JSON.parse(localStorage.getItem(key));
                    if (item.timestamp && (now - item.timestamp) > maxAge) {
                        localStorage.removeItem(key);
                        i--; // 调整索引，因为删除了一个项目
                    }
                } catch (e) {
                    // 无效的缓存项，删除
                    localStorage.removeItem(key);
                    i--;
                }
            }
        }
    }

    static limitCacheSize(maxSize) {
        const cacheKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('cache_')) {
                try {
                    const item = JSON.parse(localStorage.getItem(key));
                    cacheKeys.push({ key, timestamp: item.timestamp || 0 });
                } catch (e) {
                    // 无效的缓存项
                    localStorage.removeItem(key);
                }
            }
        }

        if (cacheKeys.length > maxSize) {
            // 按时间戳排序，删除最旧的项目
            cacheKeys.sort((a, b) => a.timestamp - b.timestamp);
            const toDelete = cacheKeys.slice(0, cacheKeys.length - maxSize);
            toDelete.forEach(item => localStorage.removeItem(item.key));
        }
    }

    static monitorPerformance() {
        // 监控页面性能
        if ('performance' in window && 'getEntriesByType' in performance) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    if (navigation) {
                        console.log('页面性能指标:', {
                            DNS查询: `${navigation.domainLookupEnd - navigation.domainLookupStart}ms`,
                            TCP连接: `${navigation.connectEnd - navigation.connectStart}ms`,
                            请求响应: `${navigation.responseEnd - navigation.requestStart}ms`,
                            DOM解析: `${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`,
                            页面加载: `${navigation.loadEventEnd - navigation.loadEventStart}ms`,
                            总时间: `${navigation.loadEventEnd - navigation.navigationStart}ms`
                        });
                    }
                }, 0);
            });
        }
    }
}

// ===== 导出到全局 =====
window.GateSentinel = window.GateSentinel || {};
window.GateSentinel.Security = {
    XSSProtection,
    InputValidator,
    SecurityPolicy,
    PerformanceOptimizer
};

// ===== 自动初始化 =====
document.addEventListener('DOMContentLoaded', () => {
    SecurityPolicy.init();
    PerformanceOptimizer.init();
});
