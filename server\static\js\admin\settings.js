/**
 * 设置页面模块
 * 处理密码修改和Webhook配置
 */

class SettingsManager {
    constructor() {
        this.passwordForm = document.getElementById('passwordForm');
        this.webhookForm = document.getElementById('webhookForm');
        this.config = {};

        this.init();
    }

    init() {
        // 检查认证
        if (!GateSentinel.apiClient.token) {
            window.location.href = '/login';
            return;
        }

        // 绑定事件
        this.bindEvents();

        // 加载设置
        this.loadSettings();

        // 初始化密码切换功能
        this.initPasswordToggles();

        // 更新系统信息
        this.updateSystemInfo();
    }

    bindEvents() {
        // 密码表单
        if (this.passwordForm) {
            this.passwordForm.addEventListener('submit', (e) => this.handlePasswordSubmit(e));

            // 密码强度检查
            const newPasswordInput = document.getElementById('newPassword');
            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', () => this.checkPasswordStrength());
            }
        }

        // Webhook表单
        if (this.webhookForm) {
            this.webhookForm.addEventListener('submit', (e) => this.handleWebhookSubmit(e));

            // Webhook启用状态切换
            const webhookEnable = document.getElementById('webhookEnable');
            if (webhookEnable) {
                webhookEnable.addEventListener('change', () => this.toggleWebhookFields());
            }
        }
    }

    async loadSettings() {
        try {
            GateSentinel.loading.show('加载设置中...');

            this.config = await GateSentinel.apiClient.get('/web/admin/api/config');

            this.updateUI();

        } catch (error) {
            console.error('加载设置失败:', error);
            GateSentinel.notification.error('加载设置失败: ' + error.message);
        } finally {
            GateSentinel.loading.hide();
        }
    }

    updateUI() {
        // 更新Webhook设置
        const webhookEnable = document.getElementById('webhookEnable');
        const webhookURL = document.getElementById('webhookURL');
        const webhookKey = document.getElementById('webhookKey');

        if (webhookEnable) {
            webhookEnable.checked = this.config.webhook_enable || false;
        }
        if (webhookURL) {
            webhookURL.value = this.config.webhook_url || '';
        }
        if (webhookKey) {
            webhookKey.value = this.config.webhook_key || '';
        }

        // 切换Webhook字段状态
        this.toggleWebhookFields();
    }

    toggleWebhookFields() {
        const webhookEnable = document.getElementById('webhookEnable');
        const webhookURL = document.getElementById('webhookURL');
        const webhookKey = document.getElementById('webhookKey');

        const isEnabled = webhookEnable && webhookEnable.checked;

        if (webhookURL) {
            webhookURL.disabled = !isEnabled;
            webhookURL.required = isEnabled;
        }
        if (webhookKey) {
            webhookKey.disabled = !isEnabled;
        }
    }

    checkPasswordStrength() {
        const password = document.getElementById('newPassword').value;
        const strengthIndicator = document.getElementById('passwordStrength');

        if (!strengthIndicator) {
            // 创建密码强度指示器
            const indicator = document.createElement('div');
            indicator.id = 'passwordStrength';
            indicator.className = 'mt-2';
            document.getElementById('newPassword').parentNode.appendChild(indicator);
        }

        const strength = this.calculatePasswordStrength(password);
        const indicator = document.getElementById('passwordStrength');

        indicator.innerHTML = `
            <div class="progress" style="height: 4px;">
                <div class="progress-bar bg-${strength.color}" style="width: ${strength.percentage}%"></div>
            </div>
            <small class="text-${strength.color}">${strength.text}</small>
        `;
    }

    calculatePasswordStrength(password) {
        let score = 0;

        if (password.length >= 8) score += 25;
        if (password.length >= 12) score += 25;
        if (/[a-z]/.test(password)) score += 10;
        if (/[A-Z]/.test(password)) score += 10;
        if (/[0-9]/.test(password)) score += 10;
        if (/[^A-Za-z0-9]/.test(password)) score += 20;

        if (score < 30) {
            return { percentage: score, color: 'danger', text: '密码强度：弱' };
        } else if (score < 60) {
            return { percentage: score, color: 'warning', text: '密码强度：中等' };
        } else if (score < 90) {
            return { percentage: score, color: 'info', text: '密码强度：强' };
        } else {
            return { percentage: score, color: 'success', text: '密码强度：很强' };
        }
    }

    async handlePasswordSubmit(e) {
        e.preventDefault();

        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // 验证密码
        if (!this.validatePassword(newPassword, confirmPassword)) {
            return;
        }

        try {
            GateSentinel.loading.show('更新密码中...');

            await GateSentinel.apiClient.post('/web/admin/api/config', {
                admin_pass: newPassword
            });

            GateSentinel.notification.success('密码修改成功');
            this.passwordForm.reset();

            // 清除密码强度指示器
            const strengthIndicator = document.getElementById('passwordStrength');
            if (strengthIndicator) {
                strengthIndicator.remove();
            }

        } catch (error) {
            console.error('密码修改失败:', error);
            GateSentinel.notification.error('密码修改失败: ' + error.message);
        } finally {
            GateSentinel.loading.hide();
        }
    }

    validatePassword(newPassword, confirmPassword) {
        if (!newPassword) {
            GateSentinel.notification.warning('请输入新密码');
            return false;
        }

        if (newPassword.length < 6) {
            GateSentinel.notification.warning('密码长度至少6位');
            return false;
        }

        if (newPassword !== confirmPassword) {
            GateSentinel.notification.warning('两次输入的密码不一致');
            return false;
        }

        return true;
    }

    async handleWebhookSubmit(e) {
        e.preventDefault();

        const webhookEnable = document.getElementById('webhookEnable').checked;
        const webhookURL = document.getElementById('webhookURL').value.trim();
        const webhookKey = document.getElementById('webhookKey').value.trim();

        // 验证Webhook设置
        if (!this.validateWebhookSettings(webhookEnable, webhookURL)) {
            return;
        }

        try {
            GateSentinel.loading.show('保存设置中...');

            await GateSentinel.apiClient.post('/web/admin/api/config', {
                webhook_enable: webhookEnable,
                webhook_url: webhookURL,
                webhook_key: webhookKey
            });

            GateSentinel.notification.success('Webhook设置保存成功');

        } catch (error) {
            console.error('Webhook设置保存失败:', error);
            GateSentinel.notification.error('Webhook设置保存失败: ' + error.message);
        } finally {
            GateSentinel.loading.hide();
        }
    }

    validateWebhookSettings(enabled, url) {
        if (enabled && !url) {
            GateSentinel.notification.warning('启用Webhook时必须填写URL');
            return false;
        }

        if (enabled && url && !this.isValidURL(url)) {
            GateSentinel.notification.warning('请输入有效的Webhook URL');
            return false;
        }

        return true;
    }

    isValidURL(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    initPasswordToggles() {
        // 新密码显示切换
        const toggleNewPassword = document.getElementById('toggleNewPassword');
        const newPasswordInput = document.getElementById('newPassword');
        if (toggleNewPassword && newPasswordInput) {
            toggleNewPassword.addEventListener('click', () => {
                const type = newPasswordInput.type === 'password' ? 'text' : 'password';
                newPasswordInput.type = type;
                const icon = toggleNewPassword.querySelector('i');
                icon.className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
            });
        }

        // 确认密码显示切换
        const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        if (toggleConfirmPassword && confirmPasswordInput) {
            toggleConfirmPassword.addEventListener('click', () => {
                const type = confirmPasswordInput.type === 'password' ? 'text' : 'password';
                confirmPasswordInput.type = type;
                const icon = toggleConfirmPassword.querySelector('i');
                icon.className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
            });
        }
    }

    updateSystemInfo() {
        // 更新系统信息
        const uptimeEl = document.getElementById('uptime');
        const connectionCountEl = document.getElementById('connectionCount');
        const lastUpdateEl = document.getElementById('lastUpdate');

        if (uptimeEl) {
            // 这里可以从API获取实际的运行时间
            uptimeEl.textContent = '运行中';
        }

        if (connectionCountEl) {
            // 这里可以从API获取实际的连接数
            connectionCountEl.textContent = '0';
        }

        if (lastUpdateEl) {
            lastUpdateEl.textContent = new Date().toLocaleString('zh-CN');
        }
    }

    async testWebhook() {
        const webhookURL = document.getElementById('webhookURL').value.trim();
        const webhookKey = document.getElementById('webhookKey').value.trim();

        if (!webhookURL) {
            GateSentinel.notification.warning('请先填写Webhook URL');
            return;
        }

        if (!this.isValidURL(webhookURL)) {
            GateSentinel.notification.warning('请输入有效的Webhook URL');
            return;
        }

        try {
            GateSentinel.loading.show('测试Webhook中...');

            // 发送测试消息
            const testData = {
                type: 'test',
                message: 'GateSentinel Webhook测试消息',
                timestamp: new Date().toISOString(),
                source: 'GateSentinel'
            };

            const response = await fetch(webhookURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...(webhookKey && { 'Authorization': `Bearer ${webhookKey}` })
                },
                body: JSON.stringify(testData)
            });

            if (response.ok) {
                GateSentinel.notification.success('Webhook测试成功！');
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

        } catch (error) {
            console.error('Webhook测试失败:', error);
            GateSentinel.notification.error('Webhook测试失败: ' + error.message);
        } finally {
            GateSentinel.loading.hide();
        }
    }
}

// 创建全局实例
const settingsManager = new SettingsManager();

// 兼容旧的函数调用方式
function loadSettings() {
    settingsManager.loadSettings();
}