package db

import (
	"database/sql"
	"shellgate/config"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB

func Init() error {
	db, err := sql.Open("sqlite3", config.GlobalConfig.DBPath)
	if err != nil {
		return err
	}

	DB = db
	return createTables()
}

func createTables() error {
	// 创建Beacon表
	_, err := DB.Exec(`
    CREATE TABLE IF NOT EXISTS beacons (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ip TEXT NOT NULL,
        hostname TEXT NOT NULL,
        username TEXT NOT NULL,
        process_name TEXT NOT NULL,
        process_path TEXT NOT NULL,
        process_id INTEGER NOT NULL,
        arch TEXT NOT NULL,
        os_uuid TEXT NOT NULL,
        uuid TEXT NOT NULL UNIQUE,
        first_time DATETIME NOT NULL,
        last_seen DATETIME NOT NULL,
        job TEXT,
        job_result TEXT
    )`)
	if err != nil {
		return err
	}

	// 创建管理员表
	_, err = DB.Exec(`
    CREATE TABLE IF NOT EXISTS admin_users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL
    )`)
	if err != nil {
		return err
	}

	return nil
}

// CreateBeacon 创建新的Beacon记录
func CreateBeacon(beacon *Beacon) error {
	_, err := DB.Exec(`
    INSERT INTO beacons (
        ip, hostname, username, process_name, process_path, process_id,
        arch, os_uuid, uuid, first_time, last_seen, job, job_result
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		beacon.IP, beacon.HostName, beacon.UserName, beacon.ProcessName,
		beacon.ProcessPath, beacon.ProcessID, beacon.Arch, beacon.OSUUID,
		beacon.UUID, beacon.FirstTime, beacon.LastSeen, beacon.Job,
		beacon.JobResult)
	return err
}

// GetBeaconByUUID 通过UUID获取Beacon
func GetBeaconByUUID(uuid string) (*Beacon, error) {
	beacon := &Beacon{}
	err := DB.QueryRow(`
    SELECT id, ip, hostname, username, process_name, process_path,
           process_id, arch, os_uuid, uuid, first_time, last_seen,
           job, job_result
    FROM beacons WHERE uuid = ?`, uuid).Scan(
		&beacon.ID, &beacon.IP, &beacon.HostName, &beacon.UserName,
		&beacon.ProcessName, &beacon.ProcessPath, &beacon.ProcessID,
		&beacon.Arch, &beacon.OSUUID, &beacon.UUID, &beacon.FirstTime,
		&beacon.LastSeen, &beacon.Job, &beacon.JobResult)
	if err != nil {
		return nil, err
	}
	return beacon, nil
}

// UpdateBeaconLastSeen 更新Beacon最后在线时间
func UpdateBeaconLastSeen(uuid string) error {
	_, err := DB.Exec("UPDATE beacons SET last_seen = ? WHERE uuid = ?",
		time.Now(), uuid)
	return err
}

// UpdateBeaconJob 更新Beacon任务
func UpdateBeaconJob(uuid string, job string) error {
	_, err := DB.Exec("UPDATE beacons SET job = ? WHERE uuid = ?",
		job, uuid)
	return err
}

// UpdateBeaconJobResult 更新Beacon任务结果
func UpdateBeaconJobResult(uuid string, result string) error {
	_, err := DB.Exec("UPDATE beacons SET job_result = ? WHERE uuid = ?",
		result, uuid)
	return err
}

// ListBeacons 获取所有Beacon列表
func ListBeacons() ([]*Beacon, error) {
	rows, err := DB.Query(`
    SELECT id, ip, hostname, username, process_name, process_path,
           process_id, arch, os_uuid, uuid, first_time, last_seen,
           job, job_result
    FROM beacons`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var beacons []*Beacon
	for rows.Next() {
		beacon := &Beacon{}
		err := rows.Scan(
			&beacon.ID, &beacon.IP, &beacon.HostName, &beacon.UserName,
			&beacon.ProcessName, &beacon.ProcessPath, &beacon.ProcessID,
			&beacon.Arch, &beacon.OSUUID, &beacon.UUID, &beacon.FirstTime,
			&beacon.LastSeen, &beacon.Job, &beacon.JobResult)
		if err != nil {
			return nil, err
		}
		beacons = append(beacons, beacon)
	}
	return beacons, nil
}

// DeleteBeaconByUUID 通过UUID删除Beacon
func DeleteBeaconByUUID(uuid string) error {
	result, err := DB.Exec("DELETE FROM beacons WHERE uuid = ?", uuid)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return sql.ErrNoRows
	}

	return nil
}
