package main

import (
	"log"
	"net"
	"shellgate/config"
	"shellgate/db"
)

func main() {
	// 设置为release模式（必须在创建gin实例前设置）
	//gin.SetMode(gin.ReleaseMode)

	// 初始化配置
	if err := config.Init(); err != nil {
		log.Fatalf("Failed to init config: %v", err)
	}

	// 初始化数据库
	if err := db.Init(); err != nil {
		log.Fatalf("Failed to init database: %v", err)
	}

	// 设置路由
	r := setupRouter()

	// 设置受信任代理
	// 仅信任本地回环地址和私有网络
	trustedCIDRs := []string{
		"127.0.0.1/8",    // IPv4 回环地址
		"10.0.0.0/8",     // RFC1918 私有网络
		"**********/12",  // RFC1918 私有网络
		"***********/16", // RFC1918 私有网络
	}

	trustedProxies := make([]string, 0)
	for _, cidr := range trustedCIDRs {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			log.Printf("Warning: Invalid CIDR %s: %v", cidr, err)
			continue
		}
		trustedProxies = append(trustedProxies, network.String())
	}

	if err := r.SetTrustedProxies(trustedProxies); err != nil {
		log.Printf("Warning: Failed to set trusted proxies: %v", err)
	}

	// 启动服务器
	if err := r.Run(config.GlobalConfig.Port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
