package utils

import (
	"encoding/base64"
	"encoding/json"
)

// EncodeBase64 将数据编码为Base64字符串
func EncodeBase64(data interface{}) (string, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(jsonData), nil
}

// DecodeBase64 将Base64字符串解码为结构体
func DecodeBase64(str string, v interface{}) error {
	data, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, v)
}
