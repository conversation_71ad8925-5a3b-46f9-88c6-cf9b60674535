<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShellGate - Beacon列表</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/web/admin">ShellGate</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="/web/admin/beacons">Beacon列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/web/admin/settings">设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面头部 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="bi bi-broadcast text-primary me-2"></i>
                            Beacon管理
                        </h2>
                        <p class="text-muted mb-0">管理和监控所有连接的Beacon客户端</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="beaconManager.loadBeacons()" title="刷新列表">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-funnel"></i> 筛选
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="beaconManager.filterByStatus('all')">全部</a></li>
                                <li><a class="dropdown-item" href="#" onclick="beaconManager.filterByStatus('online')">在线</a></li>
                                <li><a class="dropdown-item" href="#" onclick="beaconManager.filterByStatus('offline')">离线</a></li>
                                <li><a class="dropdown-item" href="#" onclick="beaconManager.filterByStatus('disconnected')">断线</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">在线</h6>
                                <h3 class="mb-0" id="onlineCount">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-circle-fill" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">离线</h6>
                                <h3 class="mb-0" id="offlineCount">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-circle-fill" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">断线</h6>
                                <h3 class="mb-0" id="disconnectedCount">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-circle-fill" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">总计</h6>
                                <h3 class="mb-0" id="totalCount">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-collection" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Beacon列表 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul me-2"></i>
                        Beacon列表
                    </h5>
                    <small class="text-muted">
                        <i class="bi bi-clock me-1"></i>
                        自动刷新：30秒
                    </small>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 80px;">状态</th>
                                <th>主机信息</th>
                                <th>网络信息</th>
                                <th>进程信息</th>
                                <th>时间信息</th>
                                <th>任务状态</th>
                                <th style="width: 120px;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="beaconList">
                            <!-- 通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务模态框 -->
    <div class="modal fade" id="taskModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">下发任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="taskForm">
                        <input type="hidden" id="beaconUUID">
                        <div class="mb-3">
                            <label class="form-label">任务类型</label>
                            <select class="form-select" id="taskType">
                                <option value="NULL">保持Sleep</option>
                                <option value="0x1A">设置Sleep时间</option>
                                <option value="0x1B">获取进程列表</option>
                                <option value="0x1C">执行Shellcode</option>
                            </select>
                        </div>
                        <div class="mb-3" id="sleepTimeDiv" style="display: none;">
                            <label class="form-label">Sleep时间（秒）</label>
                            <input type="number" class="form-control" id="sleepTime">
                        </div>
                        <div class="mb-3" id="shellcodeDiv" style="display: none;">
                            <label class="form-label">Shellcode文件</label>
                            <input type="file" class="form-control" id="shellcodeFile">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="sendTask">发送任务</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/security.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/components.js"></script>
    <script src="/static/js/admin/beacons.js"></script>
</body>
</html> 