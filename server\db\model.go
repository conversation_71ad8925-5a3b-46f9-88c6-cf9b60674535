package db

import (
	"time"
)

type Beacon struct {
	ID          int64     `json:"id"`
	IP          string    `json:"ip"`
	HostName    string    `json:"hostname"`
	UserName    string    `json:"username"`
	ProcessName string    `json:"process_name"`
	ProcessPath string    `json:"process_path"`
	ProcessID   int       `json:"process_id"`
	Arch        string    `json:"arch"`
	OSUUID      string    `json:"os_uuid"`
	UUID        string    `json:"uuid"`
	FirstTime   time.Time `json:"first_time,omitempty"`
	LastSeen    time.Time `json:"last_seen,omitempty"`
	Job         string    `json:"job"`
	JobResult   string    `json:"job_result"`
}

type BeaconRegisterInfo struct {
	HostName    string `json:"hostname"`
	UserName    string `json:"username"`
	ProcessName string `json:"process_name"`
	ProcessPath string `json:"process_path"`
	ProcessID   int    `json:"process_id"`
	Arch        string `json:"arch"`
	OSUUID      string `json:"os_uuid"`
}

type AdminUser struct {
	ID       int64  `json:"id"`
	Username string `json:"username"`
	Password string `json:"password"`
}
