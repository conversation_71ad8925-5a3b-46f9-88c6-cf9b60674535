<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShellGate - Beacon列表</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">ShellGate</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="/static/html/index.html">Beacon列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/static/html/settings.html">设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Beacon列表</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>状态</th>
                                <th>主机名</th>
                                <th>用户名</th>
                                <th>IP地址</th>
                                <th>进程</th>
                                <th>架构</th>
                                <th>首次上线</th>
                                <th>最后在线</th>
                                <th>任务状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="beaconList">
                            <!-- 通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务模态框 -->
    <div class="modal fade" id="taskModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">下发任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="taskForm">
                        <input type="hidden" id="beaconUUID">
                        <div class="mb-3">
                            <label class="form-label">任务类型</label>
                            <select class="form-select" id="taskType">
                                <option value="NULL">保持Sleep</option>
                                <option value="0x1A">设置Sleep时间</option>
                                <option value="0x1B">获取进程列表</option>
                                <option value="0x1C">执行Shellcode</option>
                            </select>
                        </div>
                        <div class="mb-3" id="sleepTimeDiv" style="display: none;">
                            <label class="form-label">Sleep时间（秒）</label>
                            <input type="number" class="form-control" id="sleepTime">
                        </div>
                        <div class="mb-3" id="shellcodeDiv" style="display: none;">
                            <label class="form-label">Shellcode文件</label>
                            <input type="file" class="form-control" id="shellcodeFile">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="sendTask">发送任务</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/index.js"></script>
</body>
</html> 