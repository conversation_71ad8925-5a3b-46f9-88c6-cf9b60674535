<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证 - GateSentinel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .url { font-family: monospace; background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🔧 GateSentinel 修复验证</h1>
    
    <div class="test-card">
        <h2>📋 修复内容</h2>
        <ul>
            <li>✅ 修复登录页面路径: <span class="url">static/html/login.html</span></li>
            <li>✅ 修复根路径重定向: <span class="url">/</span> → <span class="url">/web/login</span></li>
            <li>✅ 添加兼容重定向: <span class="url">/login</span> → <span class="url">/web/login</span></li>
            <li>✅ 优化前端token验证逻辑</li>
        </ul>
    </div>

    <div class="test-card">
        <h2>🧪 自动测试</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="clearResults()">清空结果</button>
        <div id="testResults"></div>
    </div>

    <div class="test-card">
        <h2>🔗 快速链接</h2>
        <p>点击以下链接测试各个页面：</p>
        <button onclick="window.open('http://127.0.0.1:8080', '_blank')">根路径 (/)</button>
        <button onclick="window.open('http://127.0.0.1:8080/login', '_blank')">旧登录路径 (/login)</button>
        <button onclick="window.open('http://127.0.0.1:8080/web/login', '_blank')">新登录路径 (/web/login)</button>
        <button onclick="window.open('http://127.0.0.1:8080/static/html/test-login.html', '_blank')">API测试页面</button>
    </div>

    <div class="test-card">
        <h2>📊 预期结果</h2>
        <div class="status info">
            <strong>正常流程:</strong><br>
            1. 访问根路径 → 自动跳转到 /web/login<br>
            2. 显示登录页面（美化后的界面）<br>
            3. 使用 admin/admin123 登录<br>
            4. 登录成功后跳转到 /web/admin/beacons<br>
            5. 显示Beacon管理页面
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `${message} <small>(${new Date().toLocaleTimeString()})</small>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        async function testEndpoint(url, description) {
            try {
                const response = await fetch(url, { method: 'GET' });
                if (response.ok) {
                    addResult(`✅ ${description}: ${response.status} ${response.statusText}`, 'success');
                } else if (response.status === 302) {
                    addResult(`🔄 ${description}: ${response.status} 重定向正常`, 'info');
                } else if (response.status === 401) {
                    addResult(`🔒 ${description}: ${response.status} 需要认证（正常）`, 'warning');
                } else {
                    addResult(`❌ ${description}: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ ${description}: 网络错误 - ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            try {
                const response = await fetch('/web/admin/api/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 登录API测试: 成功获取Token`, 'success');
                    return data.token;
                } else {
                    const data = await response.json();
                    addResult(`❌ 登录API测试: ${data.error}`, 'error');
                    return null;
                }
            } catch (error) {
                addResult(`❌ 登录API测试: ${error.message}`, 'error');
                return null;
            }
        }

        async function testBeaconsWithToken(token) {
            if (!token) return;

            try {
                const response = await fetch('/web/admin/api/beacons', {
                    method: 'GET',
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    const count = data.data ? data.data.length : 0;
                    addResult(`✅ Beacon API测试: 成功获取 ${count} 个Beacon`, 'success');
                } else {
                    addResult(`❌ Beacon API测试: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Beacon API测试: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            clearResults();
            addResult('🚀 开始运行测试...', 'info');

            // 测试页面端点
            await testEndpoint('/', '根路径重定向');
            await testEndpoint('/login', '旧登录路径重定向');
            await testEndpoint('/web/login', '登录页面');
            await testEndpoint('/web/admin/beacons', 'Beacon管理页面（需要认证）');

            // 测试API
            addResult('🔐 测试登录API...', 'info');
            const token = await testLogin();
            
            if (token) {
                addResult('🔍 测试Beacon API...', 'info');
                await testBeaconsWithToken(token);
            }

            addResult('✨ 测试完成！', 'success');
        }

        // 页面加载时显示状态
        document.addEventListener('DOMContentLoaded', function() {
            addResult('📄 测试页面已加载，服务器应该运行在 http://127.0.0.1:8080', 'info');
        });
    </script>
</body>
</html>
