package router

import (
	"shellgate/handler"

	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter() *gin.Engine {
	r := gin.Default()

	// 静态文件
	r.Static("/static", "./static")

	// 根路径重定向到登录页面
	r.GET("/", func(c *gin.Context) {
		c.Redirect(302, "/web/login")
	})

	// 兼容旧的登录路径
	r.GET("/login", func(c *gin.Context) {
		c.Redirect(302, "/web/login")
	})

	// 注册接口
	r.POST("/register", handler.RegisterHandler)
	r.Any("/api.jsp", handler.BeaconHandler)

	// Web界面
	web := r.Group("/web")
	{
		// 管理员登录页面（无需认证）
		web.GET("/login", handler.LoginPageHandler)
		web.POST("/login", handler.AdminLoginHandler)

		// 登录API（无需认证）
		web.POST("/admin/api/login", handler.AdminLoginHandler)

		// 需要认证的管理页面
		admin := web.Group("/admin")
		admin.Use(handler.AuthRequired())
		{
			// 页面路由
			admin.GET("", handler.IndexPageHandler)
			admin.GET("/beacons", handler.BeaconsPageHandler)
			admin.GET("/details/:uuid", handler.DetailsPageHandler)
			admin.GET("/settings", handler.SettingsPageHandler)

			// API路由
			admin.GET("/api/beacons", handler.ListBeaconsHandler)
			admin.GET("/api/beacons/:uuid", handler.GetBeaconHandler)
			admin.POST("/api/beacons/:uuid/job", handler.UpdateBeaconJobHandler)
			admin.POST("/api/beacons/:uuid/delete", handler.DeleteBeaconHandler)
			admin.GET("/api/config", handler.GetConfigHandler)
			admin.POST("/api/config", handler.UpdateConfigHandler)
		}
	}

	return r
}
