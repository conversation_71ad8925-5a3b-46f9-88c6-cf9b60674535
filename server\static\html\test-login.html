<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试 - GateSentinel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 2rem;
            max-width: 500px;
            width: 100%;
        }
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-card">
        <div class="text-center mb-4">
            <i class="bi bi-shield-check text-primary" style="font-size: 3rem;"></i>
            <h2 class="mt-2">GateSentinel 登录测试</h2>
            <p class="text-muted">测试登录功能和API连接</p>
        </div>

        <div class="row mb-3">
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h6>默认管理员账户</h6>
                        <p class="mb-1"><strong>用户名:</strong> admin</p>
                        <p class="mb-0"><strong>密码:</strong> admin123</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h6>API端点</h6>
                        <p class="mb-1"><strong>登录:</strong> /web/admin/api/login</p>
                        <p class="mb-0"><strong>Beacon:</strong> /web/admin/api/beacons</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label class="form-label">用户名</label>
            <input type="text" class="form-control" id="username" value="admin">
        </div>

        <div class="mb-3">
            <label class="form-label">密码</label>
            <input type="password" class="form-control" id="password" value="admin123">
        </div>

        <div class="d-grid gap-2">
            <button class="btn btn-primary" onclick="testLogin()">
                <i class="bi bi-play-circle me-2"></i>测试登录
            </button>
            <button class="btn btn-outline-secondary" onclick="testBeacons()" id="testBeaconsBtn" disabled>
                <i class="bi bi-list me-2"></i>测试获取Beacon列表
            </button>
            <button class="btn btn-outline-info" onclick="clearResults()">
                <i class="bi bi-trash me-2"></i>清空结果
            </button>
        </div>

        <div id="results"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let authToken = null;

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div>${message}</div>
                    <small>${new Date().toLocaleTimeString()}</small>
                </div>
            `;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            authToken = null;
            document.getElementById('testBeaconsBtn').disabled = true;
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            addResult('🔄 开始测试登录...', 'info');

            try {
                const response = await fetch('/web/admin/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.token;
                    addResult(`✅ 登录成功！<br>Token: ${data.token.substring(0, 20)}...`, 'success');
                    document.getElementById('testBeaconsBtn').disabled = false;
                } else {
                    addResult(`❌ 登录失败: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function testBeacons() {
            if (!authToken) {
                addResult('❌ 请先登录获取Token', 'error');
                return;
            }

            addResult('🔄 开始测试获取Beacon列表...', 'info');

            try {
                const response = await fetch('/web/admin/api/beacons', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    const count = data.data ? data.data.length : 0;
                    addResult(`✅ 获取Beacon列表成功！<br>找到 ${count} 个Beacon`, 'success');
                    
                    if (count > 0) {
                        const beacon = data.data[0];
                        addResult(`📋 第一个Beacon信息:<br>UUID: ${beacon.uuid}<br>主机名: ${beacon.hostname || '未知'}<br>IP: ${beacon.ip || '未知'}`, 'info');
                    }
                } else {
                    addResult(`❌ 获取Beacon列表失败: ${data.error}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 测试页面已加载，可以开始测试', 'info');
        });
    </script>
</body>
</html>
