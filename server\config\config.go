package config

type Config struct {
	Port          string `json:"port"`
	DBPath        string `json:"db_path"`
	AdminUser     string `json:"admin_user"`
	AdminPass     string `json:"admin_pass"`
	ClientToken   string `json:"client_token"`
	WebhookURL    string `json:"webhook_url"`
	WebhookKey    string `json:"webhook_key"`
	WebhookEnable bool   `json:"webhook_enable"`
}

var GlobalConfig = &Config{
	Port:        ":8080",
	DBPath:      "./shellgate.db",
	AdminUser:   "admin",
	AdminPass:   "admin123",
	ClientToken: "Demo",
}

func Init() error {
	// TODO: 从文件加载配置
	return nil
}
