#include <windows.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <stdio.h>
#include "tasks.h"

// 设置Sleep时间
BOOL Task_SetSleep(DWORD sleepTime) {
    if (g_sleep_time != NULL) {
        g_sleep_time = sleepTime;
        return TRUE;
    }
    return FALSE;
}

// 获取进程路径
BOOL GetProcessPath(DWORD processId, WCHAR* processPath, DWORD pathSize) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (hProcess == NULL) {
        return FALSE;
    }

    BOOL result = GetModuleFileNameExW(hProcess, NULL, processPath, pathSize);
    CloseHandle(hProcess);
    return result;
}
