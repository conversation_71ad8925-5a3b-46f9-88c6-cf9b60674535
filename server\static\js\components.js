/**
 * GateSentinel UI组件库
 * 提供可复用的UI组件和工具函数
 */

// ===== 导航栏组件 =====
class NavigationComponent {
    constructor() {
        this.currentPage = this.getCurrentPage();
        this.init();
    }

    init() {
        this.updateActiveNavigation();
        this.addLogoutHandler();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        if (path.includes('/beacons')) return 'beacons';
        if (path.includes('/details')) return 'details';
        if (path.includes('/settings')) return 'settings';
        return 'unknown';
    }

    updateActiveNavigation() {
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href.includes(this.currentPage)) {
                link.classList.add('active');
            }
        });
    }

    addLogoutHandler() {
        // 可以添加退出登录功能
        const navbarBrand = document.querySelector('.navbar-brand');
        if (navbarBrand) {
            navbarBrand.addEventListener('dblclick', () => {
                if (confirm('确定要退出登录吗？')) {
                    GateSentinel.apiClient.clearToken();
                    window.location.href = '/login';
                }
            });
        }
    }
}

// ===== 模态框组件 =====
class ModalComponent {
    constructor(options = {}) {
        this.options = {
            id: options.id || 'dynamicModal',
            title: options.title || '提示',
            body: options.body || '',
            size: options.size || '', // sm, lg, xl
            backdrop: options.backdrop !== false,
            keyboard: options.keyboard !== false,
            buttons: options.buttons || []
        };
        
        this.modal = null;
        this.element = null;
    }

    create() {
        // 移除已存在的模态框
        const existing = document.getElementById(this.options.id);
        if (existing) {
            existing.remove();
        }

        // 创建模态框HTML
        const modalHTML = `
            <div class="modal fade" id="${this.options.id}" tabindex="-1" 
                 data-bs-backdrop="${this.options.backdrop ? 'true' : 'static'}"
                 data-bs-keyboard="${this.options.keyboard}">
                <div class="modal-dialog ${this.options.size ? 'modal-' + this.options.size : ''}">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${this.options.title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.options.body}
                        </div>
                        ${this.options.buttons.length > 0 ? this.createFooter() : ''}
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.element = document.getElementById(this.options.id);
        this.modal = new bootstrap.Modal(this.element);

        // 绑定按钮事件
        this.bindButtonEvents();

        return this;
    }

    createFooter() {
        const buttons = this.options.buttons.map(btn => {
            const classes = btn.class || 'btn-secondary';
            const dismiss = btn.dismiss ? 'data-bs-dismiss="modal"' : '';
            return `<button type="button" class="btn ${classes}" ${dismiss} data-action="${btn.action || ''}">${btn.text}</button>`;
        }).join('');

        return `<div class="modal-footer">${buttons}</div>`;
    }

    bindButtonEvents() {
        if (!this.element) return;

        this.options.buttons.forEach(btn => {
            if (btn.action && btn.handler) {
                const button = this.element.querySelector(`[data-action="${btn.action}"]`);
                if (button) {
                    button.addEventListener('click', btn.handler);
                }
            }
        });
    }

    show() {
        if (this.modal) {
            this.modal.show();
        }
        return this;
    }

    hide() {
        if (this.modal) {
            this.modal.hide();
        }
        return this;
    }

    destroy() {
        if (this.modal) {
            this.modal.dispose();
        }
        if (this.element) {
            this.element.remove();
        }
    }

    // 静态方法：快速创建确认对话框
    static confirm(title, message, onConfirm, onCancel) {
        const modal = new ModalComponent({
            id: 'confirmModal',
            title: title,
            body: `<p>${message}</p>`,
            buttons: [
                {
                    text: '取消',
                    class: 'btn-secondary',
                    dismiss: true,
                    action: 'cancel',
                    handler: onCancel || (() => {})
                },
                {
                    text: '确认',
                    class: 'btn-primary',
                    action: 'confirm',
                    handler: () => {
                        if (onConfirm) onConfirm();
                        modal.hide();
                    }
                }
            ]
        });

        modal.create().show();
        return modal;
    }

    // 静态方法：快速创建信息对话框
    static alert(title, message, onClose) {
        const modal = new ModalComponent({
            id: 'alertModal',
            title: title,
            body: `<p>${message}</p>`,
            buttons: [
                {
                    text: '确定',
                    class: 'btn-primary',
                    dismiss: true,
                    action: 'close',
                    handler: onClose || (() => {})
                }
            ]
        });

        modal.create().show();
        return modal;
    }
}

// ===== 表格组件 =====
class TableComponent {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            columns: options.columns || [],
            data: options.data || [],
            pagination: options.pagination || false,
            pageSize: options.pageSize || 10,
            sortable: options.sortable || false,
            searchable: options.searchable || false,
            selectable: options.selectable || false,
            ...options
        };
        
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.searchTerm = '';
        this.selectedRows = new Set();
    }

    render() {
        if (!this.container) return;

        const tableHTML = `
            ${this.options.searchable ? this.createSearchBar() : ''}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        ${this.createHeader()}
                    </thead>
                    <tbody>
                        ${this.createBody()}
                    </tbody>
                </table>
            </div>
            ${this.options.pagination ? this.createPagination() : ''}
        `;

        this.container.innerHTML = tableHTML;
        this.bindEvents();
    }

    createSearchBar() {
        return `
            <div class="mb-3">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" placeholder="搜索..." id="tableSearch">
                </div>
            </div>
        `;
    }

    createHeader() {
        const selectAllCell = this.options.selectable ? 
            '<th><input type="checkbox" class="form-check-input" id="selectAll"></th>' : '';
        
        const columnCells = this.options.columns.map(col => {
            const sortable = this.options.sortable && col.sortable !== false;
            const sortIcon = sortable ? '<i class="bi bi-arrow-down-up ms-1"></i>' : '';
            const classes = sortable ? 'sortable-header' : '';
            
            return `<th class="${classes}" data-column="${col.key}">${col.title}${sortIcon}</th>`;
        }).join('');

        return `<tr>${selectAllCell}${columnCells}</tr>`;
    }

    createBody() {
        const filteredData = this.getFilteredData();
        const paginatedData = this.options.pagination ? 
            this.getPaginatedData(filteredData) : filteredData;

        if (paginatedData.length === 0) {
            const colspan = this.options.columns.length + (this.options.selectable ? 1 : 0);
            return `
                <tr>
                    <td colspan="${colspan}" class="text-center py-4 text-muted">
                        <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                        <div class="mt-2">暂无数据</div>
                    </td>
                </tr>
            `;
        }

        return paginatedData.map(row => this.createRow(row)).join('');
    }

    createRow(row) {
        const selectCell = this.options.selectable ? 
            `<td><input type="checkbox" class="form-check-input row-select" value="${row.id || ''}"></td>` : '';
        
        const dataCells = this.options.columns.map(col => {
            const value = this.getCellValue(row, col);
            return `<td>${value}</td>`;
        }).join('');

        return `<tr>${selectCell}${dataCells}</tr>`;
    }

    getCellValue(row, column) {
        const value = row[column.key];
        
        if (column.render && typeof column.render === 'function') {
            return column.render(value, row);
        }
        
        return value || '-';
    }

    getFilteredData() {
        if (!this.searchTerm) return this.options.data;
        
        return this.options.data.filter(row => {
            return this.options.columns.some(col => {
                const value = row[col.key];
                return value && value.toString().toLowerCase().includes(this.searchTerm.toLowerCase());
            });
        });
    }

    getPaginatedData(data) {
        const start = (this.currentPage - 1) * this.options.pageSize;
        const end = start + this.options.pageSize;
        return data.slice(start, end);
    }

    createPagination() {
        const filteredData = this.getFilteredData();
        const totalPages = Math.ceil(filteredData.length / this.options.pageSize);
        
        if (totalPages <= 1) return '';

        let paginationHTML = '<nav><ul class="pagination justify-content-center">';
        
        // 上一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage - 1}">上一页</a>
            </li>
        `;
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage || 
                i === 1 || 
                i === totalPages || 
                (i >= this.currentPage - 1 && i <= this.currentPage + 1)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 2 || i === this.currentPage + 2) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // 下一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage + 1}">下一页</a>
            </li>
        `;
        
        paginationHTML += '</ul></nav>';
        return paginationHTML;
    }

    bindEvents() {
        // 搜索事件
        const searchInput = this.container.querySelector('#tableSearch');
        if (searchInput) {
            searchInput.addEventListener('input', GateSentinel.Utils.debounce((e) => {
                this.searchTerm = e.target.value;
                this.currentPage = 1;
                this.render();
            }, 300));
        }

        // 排序事件
        const sortableHeaders = this.container.querySelectorAll('.sortable-header');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const column = header.dataset.column;
                if (this.sortColumn === column) {
                    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    this.sortColumn = column;
                    this.sortDirection = 'asc';
                }
                this.sortData();
                this.render();
            });
        });

        // 分页事件
        const pageLinks = this.container.querySelectorAll('.page-link[data-page]');
        pageLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = parseInt(link.dataset.page);
                if (page > 0 && page <= Math.ceil(this.getFilteredData().length / this.options.pageSize)) {
                    this.currentPage = page;
                    this.render();
                }
            });
        });

        // 选择事件
        const selectAll = this.container.querySelector('#selectAll');
        if (selectAll) {
            selectAll.addEventListener('change', (e) => {
                const rowSelects = this.container.querySelectorAll('.row-select');
                rowSelects.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                    if (e.target.checked) {
                        this.selectedRows.add(checkbox.value);
                    } else {
                        this.selectedRows.delete(checkbox.value);
                    }
                });
            });
        }

        const rowSelects = this.container.querySelectorAll('.row-select');
        rowSelects.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.selectedRows.add(e.target.value);
                } else {
                    this.selectedRows.delete(e.target.value);
                }
            });
        });
    }

    sortData() {
        if (!this.sortColumn) return;

        this.options.data.sort((a, b) => {
            const aVal = a[this.sortColumn];
            const bVal = b[this.sortColumn];
            
            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });
    }

    updateData(newData) {
        this.options.data = newData;
        this.currentPage = 1;
        this.selectedRows.clear();
        this.render();
    }

    getSelectedRows() {
        return Array.from(this.selectedRows);
    }
}

// ===== 导出组件 =====
window.GateSentinel = window.GateSentinel || {};
window.GateSentinel.Components = {
    NavigationComponent,
    ModalComponent,
    TableComponent
};

// ===== 自动初始化导航组件 =====
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.navbar')) {
        new NavigationComponent();
    }
});
