<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShellGate - 设置</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/web/admin">ShellGate</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/web/admin/beacons">Beacon列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/web/admin/settings">设置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面头部 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="bi bi-gear text-primary me-2"></i>
                            系统设置
                        </h2>
                        <p class="text-muted mb-0">管理系统配置和安全设置</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="settingsManager.loadSettings()" title="刷新设置">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 密码设置 -->
            <div class="col-lg-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-shield-lock me-2"></i>安全设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="passwordForm">
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">
                                    <i class="bi bi-key me-1"></i>新密码
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="newPassword"
                                           placeholder="请输入新密码" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <!-- 密码强度指示器将通过JavaScript动态添加 -->
                            </div>
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">
                                    <i class="bi bi-check-circle me-1"></i>确认密码
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirmPassword"
                                           placeholder="请再次输入密码" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-shield-check me-2"></i>更新密码
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>系统信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-sm">
                            <div class="col-6">
                                <div class="mb-2">
                                    <strong>版本:</strong> GateSentinel v1.0
                                </div>
                                <div class="mb-2">
                                    <strong>运行时间:</strong> <span id="uptime">-</span>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mb-2">
                                    <strong>连接数:</strong> <span id="connectionCount">-</span>
                                </div>
                                <div class="mb-2">
                                    <strong>最后更新:</strong> <span id="lastUpdate">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Webhook设置 -->
            <div class="col-lg-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-webhook me-2"></i>通知设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="webhookForm">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="webhookEnable">
                                    <label class="form-check-label" for="webhookEnable">
                                        <strong>启用Webhook通知</strong>
                                    </label>
                                </div>
                                <div class="form-text">开启后将向指定URL发送Beacon状态变化通知</div>
                            </div>

                            <div class="mb-3">
                                <label for="webhookURL" class="form-label">
                                    <i class="bi bi-link me-1"></i>Webhook URL
                                </label>
                                <input type="url" class="form-control" id="webhookURL"
                                       placeholder="https://api.example.com/webhook">
                                <div class="form-text">接收通知的URL地址</div>
                            </div>

                            <div class="mb-3">
                                <label for="webhookKey" class="form-label">
                                    <i class="bi bi-key me-1"></i>Webhook Key
                                </label>
                                <input type="text" class="form-control" id="webhookKey"
                                       placeholder="可选的认证密钥">
                                <div class="form-text">适配Bark推送服务或其他需要密钥的服务</div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-2"></i>保存设置
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="settingsManager.testWebhook()">
                                    <i class="bi bi-send me-2"></i>测试Webhook
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 操作日志 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>最近操作
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="recentActions" class="text-sm">
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <span>系统启动</span>
                                <small class="text-muted">刚刚</small>
                            </div>
                            <div class="text-center py-3 text-muted">
                                <small>更多操作日志将显示在这里</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/security.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/components.js"></script>
    <script src="/static/js/admin/settings.js"></script>
</body>
</html> 