// 处理登录表单提交
async function handleLogin(event) {
    event.preventDefault();

    // 获取表单数据
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        // 发送登录请求
        const response = await fetch('/web/admin/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok) {
            // 保存token到localStorage
            localStorage.setItem('token', data.token);
            // 登录成功，跳转到beacons管理页面
            window.location.href = '/web/admin/beacons';
        } else {
            // 登录失败，显示错误信息
            showError(data.error || '登录失败');
        }
    } catch (error) {
        console.error('Login error:', error);
        showError('网络错误，请稍后重试');
    }

    return false;
}

// 显示错误信息
function showError(message) {
    // 移除旧的错误提示
    const oldAlert = document.querySelector('.alert');
    if (oldAlert) {
        oldAlert.remove();
    }

    // 创建新的错误提示
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger';
    alert.role = 'alert';
    alert.textContent = message;

    // 插入到表单前面
    const form = document.getElementById('loginForm');
    form.parentNode.insertBefore(alert, form);

    // 3秒后自动消失
    setTimeout(() => {
        alert.remove();
    }, 3000);
} 