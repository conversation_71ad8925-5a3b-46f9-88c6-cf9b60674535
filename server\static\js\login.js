/**
 * 登录页面模块
 * 处理用户登录逻辑
 */

class LoginManager {
    constructor() {
        this.form = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.submitButton = this.form.querySelector('button[type="submit"]');

        this.init();
    }

    init() {
        // 检查是否已登录（验证token有效性）
        this.checkExistingLogin();

        // 绑定事件
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));

        // 添加输入验证
        this.usernameInput.addEventListener('input', () => this.validateForm());
        this.passwordInput.addEventListener('input', () => this.validateForm());

        // 初始验证
        this.validateForm();

        // 聚焦到用户名输入框
        this.usernameInput.focus();

        // 绑定密码显示切换
        this.initPasswordToggle();
    }

    async checkExistingLogin() {
        const token = localStorage.getItem('token');
        if (!token) {
            return; // 没有token，继续显示登录页面
        }

        try {
            // 验证token是否有效
            const response = await fetch('/web/admin/api/beacons', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                // Token有效，跳转到管理页面
                window.location.href = '/web/admin/beacons';
            } else {
                // Token无效，清除并继续显示登录页面
                localStorage.removeItem('token');
                GateSentinel.apiClient.clearToken();
            }
        } catch (error) {
            // 网络错误或其他问题，清除token
            console.error('验证登录状态失败:', error);
            localStorage.removeItem('token');
            GateSentinel.apiClient.clearToken();
        }
    }

    validateForm() {
        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value.trim();

        const isValid = username.length > 0 && password.length > 0;
        this.submitButton.disabled = !isValid;

        return isValid;
    }

    initPasswordToggle() {
        const toggleButton = document.getElementById('togglePassword');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                const type = this.passwordInput.type === 'password' ? 'text' : 'password';
                this.passwordInput.type = type;

                const icon = toggleButton.querySelector('i');
                icon.className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
            });
        }
    }

    async handleSubmit(event) {
        event.preventDefault();

        if (!this.validateForm()) {
            GateSentinel.notification.warning('请填写用户名和密码');
            return;
        }

        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value.trim();

        // 显示加载状态
        this.setLoading(true);

        try {
            const data = await GateSentinel.apiClient.post('/web/admin/api/login', {
                username,
                password
            });

            // 保存token
            GateSentinel.apiClient.setToken(data.token);

            // 显示成功消息
            GateSentinel.notification.success('登录成功，正在跳转...');

            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
                window.location.href = '/web/admin/beacons';
            }, 1000);

        } catch (error) {
            console.error('登录失败:', error);
            GateSentinel.notification.error(error.message || '登录失败，请检查用户名和密码');

            // 清空密码输入框
            this.passwordInput.value = '';
            this.passwordInput.focus();
        } finally {
            this.setLoading(false);
        }
    }

    setLoading(loading) {
        this.submitButton.disabled = loading;

        if (loading) {
            this.submitButton.innerHTML = `
                <div class="loading-spinner"></div>
                <span>登录中...</span>
            `;
        } else {
            this.submitButton.innerHTML = '登录';
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
});

// 兼容旧的函数调用方式
function handleLogin(event) {
    // 这个函数保留是为了兼容HTML中的onsubmit调用
    // 实际逻辑已经移到LoginManager中
    return false;
}