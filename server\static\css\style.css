/* ===== CSS 变量定义 ===== */
:root {
    /* 主色调 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    --secondary-color: #64748b;
    --secondary-hover: #475569;

    /* 状态颜色 */
    --success-color: #10b981;
    --success-light: #d1fae5;
    --warning-color: #f59e0b;
    --warning-light: #fef3c7;
    --danger-color: #ef4444;
    --danger-light: #fee2e2;
    --info-color: #06b6d4;
    --info-light: #cffafe;

    /* 中性色 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #0f172a;
    --bg-dark-secondary: #1e293b;

    /* 文字颜色 */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-light: #94a3b8;
    --text-white: #ffffff;

    /* 边框 */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    --border-focus: var(--primary-color);

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;

    /* 过渡动画 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* 暗色主题变量 */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #334155;
    --border-hover: #475569;
}

/* ===== 全局样式重置 ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== 导航栏样式 ===== */
.navbar {
    background: var(--bg-primary) !important;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.navbar-brand:hover {
    color: var(--primary-hover) !important;
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    margin: 0 var(--spacing-xs);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background-color: var(--primary-light);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background-color: var(--primary-light);
    font-weight: 600;
}

/* ===== 卡片样式 ===== */
.card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* ===== 按钮样式 ===== */
.btn {
    font-weight: 500;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-lg);
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-white);
}

.btn-secondary:hover {
    background-color: var(--secondary-hover);
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--text-white);
}

.btn-success:hover {
    background-color: #059669;
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--text-white);
}

.btn-danger:hover {
    background-color: #dc2626;
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--text-white);
}

.btn-warning:hover {
    background-color: #d97706;
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-info {
    background-color: var(--info-color);
    color: var(--text-white);
}

.btn-info:hover {
    background-color: #0891b2;
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
    line-height: 1rem;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
    line-height: 1.5rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== 表单样式 ===== */
.form-control {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: var(--text-primary);
    transition: all var(--transition-fast);
    width: 100%;
}

.form-control:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-select {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: var(--text-primary);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.form-select:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.form-text {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* 输入框组合样式 */
.input-group {
    display: flex;
    align-items: stretch;
    width: 100%;
}

.input-group-text {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-right: none;
    border-radius: var(--radius-md) 0 0 var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    align-items: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.input-group .form-control:focus {
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group .form-control:focus + .input-group-text {
    border-color: var(--border-focus);
}

/* ===== 表格样式 ===== */
.table {
    width: 100%;
    margin-bottom: 0;
    color: var(--text-primary);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-md);
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.table th {
    font-weight: 600;
    color: var(--text-secondary);
    background-color: var(--bg-tertiary);
    border-bottom: 2px solid var(--border-color);
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.table-hover tbody tr:hover {
    background-color: var(--bg-tertiary);
    transition: background-color var(--transition-fast);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--bg-secondary);
}

.table-responsive {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

/* ===== 状态徽章样式 ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
    color: var(--text-white);
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
    color: var(--text-white);
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
    color: var(--text-white);
}

.badge.bg-secondary {
    background-color: var(--secondary-color) !important;
    color: var(--text-white);
}

.badge.bg-info {
    background-color: var(--info-color) !important;
    color: var(--text-white);
}

/* ===== 状态指示器 ===== */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-dot.online {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    animation: pulse-green 2s infinite;
}

.status-dot.offline {
    background-color: var(--danger-color);
}

.status-dot.warning {
    background-color: var(--warning-color);
}

@keyframes pulse-green {
    0%, 100% {
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
    }
}

/* ===== 模态框样式 ===== */
.modal-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.modal-title {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    background-color: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.btn-close:hover {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
}

/* ===== 警告框样式 ===== */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.alert-success {
    background-color: var(--success-light);
    border-color: var(--success-color);
    color: #065f46;
}

.alert-warning {
    background-color: var(--warning-light);
    border-color: var(--warning-color);
    color: #92400e;
}

.alert-danger {
    background-color: var(--danger-light);
    border-color: var(--danger-color);
    color: #991b1b;
}

.alert-info {
    background-color: var(--info-light);
    border-color: var(--info-color);
    color: #155e75;
}

/* ===== 加载状态样式 ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

.loading-spinner.lg {
    width: 32px;
    height: 32px;
    border-width: 3px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-content {
    background-color: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-secondary) 50%, var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--radius-sm);
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: var(--spacing-xs);
}

.skeleton-text.w-full {
    width: 100%;
}

.skeleton-text.w-3-4 {
    width: 75%;
}

.skeleton-text.w-1-2 {
    width: 50%;
}

/* ===== 工具类 ===== */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.font-mono {
    font-family: var(--font-mono);
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.text-primary {
    color: var(--primary-color);
}

.text-secondary {
    color: var(--text-secondary);
}

.text-muted {
    color: var(--text-muted);
}

.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-danger {
    color: var(--danger-color);
}

.bg-primary {
    background-color: var(--bg-primary);
}

.bg-secondary {
    background-color: var(--bg-secondary);
}

.bg-tertiary {
    background-color: var(--bg-tertiary);
}

.border-0 {
    border: none;
}

.border {
    border: 1px solid var(--border-color);
}

.border-t {
    border-top: 1px solid var(--border-color);
}

.border-b {
    border-bottom: 1px solid var(--border-color);
}

.rounded {
    border-radius: var(--radius-md);
}

.rounded-lg {
    border-radius: var(--radius-lg);
}

.shadow {
    box-shadow: var(--shadow-sm);
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.hidden {
    display: none;
}

.flex {
    display: flex;
}

.inline-flex {
    display: inline-flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-2 {
    gap: var(--spacing-sm);
}

.gap-4 {
    gap: var(--spacing-md);
}

.w-full {
    width: 100%;
}

.h-full {
    height: 100%;
}

.min-h-screen {
    min-height: 100vh;
}

/* ===== 主题切换按钮 ===== */
.theme-toggle {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1000;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.theme-toggle i {
    font-size: 1.25rem;
    color: var(--text-primary);
    transition: color var(--transition-fast);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .container {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .card-header {
        padding: var(--spacing-md);
    }

    .table th,
    .table td {
        padding: var(--spacing-sm);
        font-size: 0.75rem;
    }

    .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.75rem;
    }

    .btn-sm {
        padding: 0.125rem var(--spacing-xs);
        font-size: 0.625rem;
    }

    .modal-dialog {
        margin: var(--spacing-md);
    }

    .modal-body,
    .modal-header,
    .modal-footer {
        padding: var(--spacing-md);
    }

    .navbar-brand {
        font-size: 1.25rem;
    }

    .theme-toggle {
        top: var(--spacing-md);
        right: var(--spacing-md);
        width: 40px;
        height: 40px;
    }

    .theme-toggle i {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .table-responsive {
        font-size: 0.75rem;
    }

    .table th,
    .table td {
        padding: var(--spacing-xs);
    }

    .btn-group .btn {
        padding: var(--spacing-xs);
        font-size: 0.625rem;
    }

    .card {
        margin-bottom: var(--spacing-md);
    }

    .modal-dialog {
        margin: var(--spacing-sm);
    }
}

/* ===== 特殊组件样式 ===== */
.beacon-status {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.beacon-status.online {
    background-color: var(--success-light);
    color: #065f46;
    border: 1px solid var(--success-color);
}

.beacon-status.offline {
    background-color: var(--danger-light);
    color: #991b1b;
    border: 1px solid var(--danger-color);
}

.beacon-status.warning {
    background-color: var(--warning-light);
    color: #92400e;
    border: 1px solid var(--warning-color);
}

.job-result {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-primary);
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 400px;
    overflow-y: auto;
}

.job-result:empty::before {
    content: "暂无任务结果";
    color: var(--text-muted);
    font-style: italic;
}

/* ===== 动画效果 ===== */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

.scale-in {
    animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.9);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* Firefox 滚动条 */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--bg-secondary);
}

/* ===== 打印样式 ===== */
@media print {
    .theme-toggle,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #000;
    }

    body {
        background: white !important;
        color: black !important;
    }
}