<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块调试 - GateSentinel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 GateSentinel 模块调试</h1>
    
    <div class="debug-card">
        <h2>📦 模块加载状态</h2>
        <button onclick="checkModules()">检查模块</button>
        <button onclick="testXSSProtection()">测试XSS防护</button>
        <button onclick="clearResults()">清空结果</button>
        <div id="moduleResults"></div>
    </div>

    <div class="debug-card">
        <h2>🧪 功能测试</h2>
        <button onclick="testEscapeHtml()">测试HTML转义</button>
        <button onclick="testApiClient()">测试API客户端</button>
        <button onclick="testNotification()">测试通知系统</button>
        <div id="functionResults"></div>
    </div>

    <div class="debug-card">
        <h2>📊 全局对象检查</h2>
        <button onclick="inspectGlobalObjects()">检查全局对象</button>
        <div id="globalResults"></div>
    </div>

    <!-- 按照正确顺序加载脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/security.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/components.js"></script>

    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `${message} <small>(${new Date().toLocaleTimeString()})</small>`;
            container.appendChild(div);
        }

        function clearResults() {
            ['moduleResults', 'functionResults', 'globalResults'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
        }

        function checkModules() {
            const container = 'moduleResults';
            
            // 检查window.GateSentinel
            if (window.GateSentinel) {
                addResult(container, '✅ window.GateSentinel 已定义', 'success');
                
                // 检查各个子模块
                const modules = ['Security', 'Components', 'themeManager', 'apiClient', 'notification', 'loading'];
                modules.forEach(module => {
                    if (window.GateSentinel[module]) {
                        addResult(container, `✅ GateSentinel.${module} 已定义`, 'success');
                    } else {
                        addResult(container, `❌ GateSentinel.${module} 未定义`, 'error');
                    }
                });
                
                // 检查Security子模块
                if (window.GateSentinel.Security) {
                    const securityModules = ['XSSProtection', 'InputValidator', 'SecurityPolicy', 'PerformanceOptimizer'];
                    securityModules.forEach(module => {
                        if (window.GateSentinel.Security[module]) {
                            addResult(container, `✅ GateSentinel.Security.${module} 已定义`, 'success');
                        } else {
                            addResult(container, `❌ GateSentinel.Security.${module} 未定义`, 'error');
                        }
                    });
                }
            } else {
                addResult(container, '❌ window.GateSentinel 未定义', 'error');
            }
        }

        function testXSSProtection() {
            const container = 'moduleResults';
            
            try {
                if (window.GateSentinel && window.GateSentinel.Security && window.GateSentinel.Security.XSSProtection) {
                    const testInput = '<script>alert("xss")</script>';
                    const escaped = window.GateSentinel.Security.XSSProtection.escapeHtml(testInput);
                    addResult(container, `✅ XSS防护测试成功: ${escaped}`, 'success');
                } else {
                    addResult(container, '❌ XSSProtection 不可用', 'error');
                }
            } catch (error) {
                addResult(container, `❌ XSS防护测试失败: ${error.message}`, 'error');
            }
        }

        function testEscapeHtml() {
            const container = 'functionResults';
            
            // 测试备用的HTML转义函数
            function escapeHtml(text) {
                if (window.GateSentinel && window.GateSentinel.Security && window.GateSentinel.Security.XSSProtection) {
                    return window.GateSentinel.Security.XSSProtection.escapeHtml(text);
                }
                
                if (typeof text !== 'string') return text;
                
                const map = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#039;',
                    '/': '&#x2F;'
                };
                
                return text.replace(/[&<>"'/]/g, (s) => map[s]);
            }
            
            try {
                const testCases = [
                    '<script>alert("test")</script>',
                    'Hello & "World"',
                    "It's a test",
                    '<img src="x" onerror="alert(1)">'
                ];
                
                testCases.forEach(test => {
                    const escaped = escapeHtml(test);
                    addResult(container, `✅ 转义测试: "${test}" → "${escaped}"`, 'success');
                });
            } catch (error) {
                addResult(container, `❌ HTML转义测试失败: ${error.message}`, 'error');
            }
        }

        function testApiClient() {
            const container = 'functionResults';
            
            try {
                if (window.GateSentinel && window.GateSentinel.apiClient) {
                    addResult(container, '✅ API客户端可用', 'success');
                    addResult(container, `Token状态: ${window.GateSentinel.apiClient.token ? '已设置' : '未设置'}`, 'info');
                } else {
                    addResult(container, '❌ API客户端不可用', 'error');
                }
            } catch (error) {
                addResult(container, `❌ API客户端测试失败: ${error.message}`, 'error');
            }
        }

        function testNotification() {
            const container = 'functionResults';
            
            try {
                if (window.GateSentinel && window.GateSentinel.notification) {
                    window.GateSentinel.notification.info('这是一个测试通知');
                    addResult(container, '✅ 通知系统测试成功', 'success');
                } else {
                    addResult(container, '❌ 通知系统不可用', 'error');
                }
            } catch (error) {
                addResult(container, `❌ 通知系统测试失败: ${error.message}`, 'error');
            }
        }

        function inspectGlobalObjects() {
            const container = 'globalResults';
            
            // 检查全局对象结构
            const globalStructure = {
                'window.GateSentinel': window.GateSentinel,
                'window.GateSentinel.Security': window.GateSentinel?.Security,
                'window.GateSentinel.Components': window.GateSentinel?.Components,
                'window.GateSentinel.themeManager': window.GateSentinel?.themeManager,
                'window.GateSentinel.apiClient': window.GateSentinel?.apiClient,
                'window.GateSentinel.notification': window.GateSentinel?.notification,
                'window.GateSentinel.loading': window.GateSentinel?.loading
            };
            
            const pre = document.createElement('pre');
            pre.textContent = JSON.stringify(globalStructure, (key, value) => {
                if (typeof value === 'function') return '[Function]';
                if (typeof value === 'object' && value !== null) return '[Object]';
                return value;
            }, 2);
            
            const container_el = document.getElementById(container);
            container_el.appendChild(pre);
        }

        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addResult('moduleResults', '🚀 调试页面已加载，开始检查模块...', 'info');
                checkModules();
            }, 500);
        });
    </script>
</body>
</html>
