<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务测试 - GateSentinel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log-entry {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-warning { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 任务执行测试</h1>
        
        <div class="test-card">
            <h3>📋 当前Beacon信息</h3>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>UUID:</strong> <span id="beaconUUID">bef7aa54-190e-40f3-9a5e-525612b87d08</span></p>
                    <p><strong>状态:</strong> <span id="beaconStatus" class="badge bg-success">在线</span></p>
                </div>
                <div class="col-md-6">
                    <p><strong>当前任务:</strong> <span id="currentJob">无</span></p>
                    <p><strong>最后更新:</strong> <span id="lastUpdate">-</span></p>
                </div>
            </div>
        </div>

        <div class="test-card">
            <h3>🚀 快速任务测试</h3>
            <div class="row">
                <div class="col-md-3">
                    <button class="btn btn-primary w-100" onclick="sendTestTask('0x1B')">
                        <i class="bi bi-list me-2"></i>获取进程列表
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning w-100" onclick="sendTestTask('0x1A', {sleep_time: 30})">
                        <i class="bi bi-pause me-2"></i>设置Sleep 30秒
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info w-100" onclick="checkBeaconStatus()">
                        <i class="bi bi-arrow-clockwise me-2"></i>检查状态
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-secondary w-100" onclick="clearLogs()">
                        <i class="bi bi-trash me-2"></i>清空日志
                    </button>
                </div>
            </div>
        </div>

        <div class="test-card">
            <h3>📊 实时监控</h3>
            <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                <label class="form-check-label" for="autoRefresh">
                    自动刷新 (每2秒)
                </label>
            </div>
            <div id="monitoringData" class="border rounded p-3 bg-light">
                <div class="text-center text-muted">
                    <i class="bi bi-hourglass-split"></i> 等待数据...
                </div>
            </div>
        </div>

        <div class="test-card">
            <h3>📝 操作日志</h3>
            <div id="logContainer" style="max-height: 400px; overflow-y: auto;">
                <!-- 日志将在这里显示 -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/security.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/components.js"></script>

    <script>
        let refreshInterval = null;
        let beaconUUID = 'bef7aa54-190e-40f3-9a5e-525612b87d08';

        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }

        async function sendTestTask(taskType, extraData = {}) {
            const taskData = { type: taskType, ...extraData };
            
            addLog(`🚀 发送任务: ${getTaskTypeName(taskType)}`, 'info');
            
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    addLog('❌ 未找到认证Token，请先登录', 'error');
                    return;
                }

                const response = await fetch(`/web/admin/api/beacons/${beaconUUID}/job`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(taskData)
                });

                if (response.ok) {
                    addLog('✅ 任务发送成功', 'success');
                    // 立即检查状态
                    setTimeout(() => checkBeaconStatus(), 1000);
                } else {
                    const error = await response.text();
                    addLog(`❌ 任务发送失败: ${error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function checkBeaconStatus() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    addLog('❌ 未找到认证Token', 'error');
                    return;
                }

                const response = await fetch(`/web/admin/api/beacons/${beaconUUID}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const beacon = await response.json();
                    updateBeaconDisplay(beacon);
                    
                    if (beacon.job_result && beacon.job_result.trim()) {
                        addLog(`📋 任务结果: ${beacon.job_result.substring(0, 100)}${beacon.job_result.length > 100 ? '...' : ''}`, 'success');
                    } else if (beacon.job) {
                        addLog(`⏳ 任务执行中: ${beacon.job}`, 'warning');
                    } else {
                        addLog('💤 Beacon空闲状态', 'info');
                    }
                } else {
                    addLog(`❌ 获取状态失败: ${response.status}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 检查状态失败: ${error.message}`, 'error');
            }
        }

        function updateBeaconDisplay(beacon) {
            document.getElementById('beaconUUID').textContent = beacon.uuid || beaconUUID;
            document.getElementById('currentJob').textContent = beacon.job || '无';
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            
            // 更新状态
            const statusEl = document.getElementById('beaconStatus');
            const lastSeen = beacon.last_seen;
            if (lastSeen) {
                const diffSeconds = Math.floor((Date.now() - lastSeen) / 1000);
                if (diffSeconds < 300) {
                    statusEl.className = 'badge bg-success';
                    statusEl.textContent = '在线';
                } else {
                    statusEl.className = 'badge bg-warning';
                    statusEl.textContent = '离线';
                }
            }

            // 更新监控数据
            const monitoringData = document.getElementById('monitoringData');
            monitoringData.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>主机信息:</strong><br>
                        主机名: ${beacon.hostname || '-'}<br>
                        用户: ${beacon.username || '-'}<br>
                        IP: ${beacon.ip || '-'}
                    </div>
                    <div class="col-md-6">
                        <strong>任务信息:</strong><br>
                        当前任务: ${beacon.job || '无'}<br>
                        任务结果: ${beacon.job_result ? '有结果' : '无结果'}<br>
                        结果长度: ${beacon.job_result ? beacon.job_result.length + ' 字符' : '0'}
                    </div>
                </div>
                ${beacon.job_result ? `
                    <div class="mt-3">
                        <strong>任务结果预览:</strong>
                        <pre class="mt-2 p-2 bg-white border rounded" style="max-height: 200px; overflow-y: auto;">${beacon.job_result}</pre>
                    </div>
                ` : ''}
            `;
        }

        function getTaskTypeName(type) {
            const taskTypes = {
                '0x00': '保持Sleep',
                '0x1A': '设置Sleep时间',
                '0x1B': '获取进程列表',
                '0x1C': '执行Shellcode'
            };
            return taskTypes[type] || '未知任务';
        }

        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            
            refreshInterval = setInterval(() => {
                if (document.getElementById('autoRefresh').checked) {
                    checkBeaconStatus();
                }
            }, 2000);
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 任务测试页面已加载', 'info');
            
            // 检查登录状态
            const token = localStorage.getItem('token');
            if (token) {
                addLog('✅ 已找到认证Token', 'success');
                checkBeaconStatus();
                startAutoRefresh();
            } else {
                addLog('⚠️ 未找到认证Token，请先登录', 'warning');
            }

            // 绑定自动刷新开关
            document.getElementById('autoRefresh').addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                    addLog('🔄 已启用自动刷新', 'info');
                } else {
                    stopAutoRefresh();
                    addLog('⏸️ 已停用自动刷新', 'info');
                }
            });
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', stopAutoRefresh);
    </script>
</body>
</html>
